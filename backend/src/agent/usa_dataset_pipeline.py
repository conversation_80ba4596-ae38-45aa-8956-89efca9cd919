"""
USA Dataset Pipeline

This module handles power plant data extraction specifically for USA-based plants
using the usa_details.xlsx dataset. It prioritizes this dataset over web search
for US plants and provides fallback to existing web search logic for non-USA plants.

Key Features:
1. Country detection to route to appropriate pipeline
2. Excel dataset loading and processing using Gemini LLM
3. Organization, Plant, and Unit level JSON generation
4. Emission factor calculations for coal plants
5. Fuel type mapping and timestamp formatting
"""

import os
import pandas as pd
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from langchain_google_genai import ChatGoogleGenerativeAI
from dotenv import load_dotenv

load_dotenv()

class USADatasetPipeline:
    """Main class for handling USA power plant data extraction"""
    
    def __init__(self):
        self.dataset_path = "USA Details.xlsx"

        # Get API key from environment
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            print("⚠️ Warning: GEMINI_API_KEY not found in environment variables")
            # Try to load from .env file explicitly
            from dotenv import load_dotenv
            load_dotenv()
            api_key = os.getenv("GEMINI_API_KEY")

        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-pro",
            temperature=0,
            api_key=api_key
        )
        # Keep gemini_model for backward compatibility
        self.gemini_model = self.llm
        self.dataset_loaded = False
        self.usa_details = None
        self.yearly_sheets = {}
        self.full_names = None

    def _parse_llm_json_response(self, response_content: str) -> dict:
        """
        Parse JSON response from LLM, handling markdown code blocks

        Args:
            response_content: Raw response content from LLM

        Returns:
            Parsed JSON dictionary
        """
        import json

        # Extract JSON from markdown code blocks if present
        content = response_content.strip()
        if content.startswith('```json') and content.endswith('```'):
            # Remove markdown code block formatting
            content = content[7:-3].strip()  # Remove ```json and ```
        elif content.startswith('```') and content.endswith('```'):
            # Remove generic code block formatting
            content = content[3:-3].strip()

        return json.loads(content)

    def is_usa_plant(self, plant_name: str, organization_name: str = None) -> bool:
        """
        Detect if a power plant is in the United States
        
        Args:
            plant_name: Name of the power plant
            organization_name: Optional organization name
            
        Returns:
            bool: True if plant is in USA, False otherwise
        """
        try:
            # Load dataset if not already loaded
            if not self.dataset_loaded:
                self._load_dataset()
            
            if self.usa_details is None:
                return False
                
            # Check if plant exists in USA Details sheet
            plant_matches = self.usa_details[
                self.usa_details['Plant Name'].str.contains(plant_name, case=False, na=False)
            ]
            
            if not plant_matches.empty:
                print(f"✅ Found {plant_name} in USA dataset")
                return True
                
            # Also check by organization name if provided
            if organization_name:
                org_matches = self.usa_details[
                    self.usa_details['Entity Name'].str.contains(organization_name, case=False, na=False)
                ]
                if not org_matches.empty:
                    print(f"✅ Found organization {organization_name} in USA dataset")
                    return True
                    
            print(f"❌ {plant_name} not found in USA dataset")
            return False
            
        except Exception as e:
            print(f"⚠️ Error checking USA plant status: {e}")
            return False
    
    def _load_dataset(self):
        """Load the USA dataset from separated Excel files in usa_database folder"""
        try:
            print("📊 Loading USA dataset from separated sheets...")

            # Define paths for each sheet in usa_database folder
            base_path = "usa_database"
            usa_details_path = f"{base_path}/USA_Details.xlsx"
            full_names_path = f"{base_path}/Full Names.xlsx"

            # Check if usa_database folder exists
            if not os.path.exists(base_path):
                print(f"❌ USA database folder not found: {base_path}")
                return False

            # Load USA Details sheet
            print(f"   📋 Loading USA Details from: {usa_details_path}")
            if os.path.exists(usa_details_path):
                self.usa_details = pd.read_excel(usa_details_path)
                print(f"   ✅ USA Details loaded: {self.usa_details.shape}")
                print(f"   📋 USA Details columns: {list(self.usa_details.columns)}")
            else:
                print(f"   ❌ USA Details file not found: {usa_details_path}")
                return False

            # Load yearly sheets (2020-2024) from separate files
            for year in ['2020', '2021', '2022', '2023', '2024']:
                try:
                    yearly_path = f"{base_path}/{year}.xlsx"
                    print(f"   📊 Loading {year} data from: {yearly_path}")
                    if os.path.exists(yearly_path):
                        self.yearly_sheets[year] = pd.read_excel(yearly_path)
                        print(f"   ✅ {year} data loaded: {self.yearly_sheets[year].shape}")
                    else:
                        print(f"   ⚠️ {year} file not found: {yearly_path}")
                except Exception as e:
                    print(f"   ⚠️ Could not load {year} sheet: {e}")

            # Load Full Names sheet for emission factor mapping
            try:
                print(f"   🏷️ Loading Full Names from: {full_names_path}")
                if os.path.exists(full_names_path):
                    self.full_names = pd.read_excel(full_names_path)
                    print(f"   ✅ Full Names loaded: {self.full_names.shape}")
                else:
                    print(f"   ⚠️ Full Names file not found: {full_names_path}")
                    self.full_names = None
            except Exception as e:
                print(f"   ⚠️ Could not load Full Names sheet: {e}")
                self.full_names = None

            self.dataset_loaded = True
            print("✅ USA dataset loaded successfully from separated sheets")
            return True

        except Exception as e:
            print(f"❌ Error loading USA dataset: {e}")
            return False

    def _prepare_excel_data_for_llm(self, plant_name: str, data_type: str = "organization") -> str:
        """
        Prepare Excel data in text format for LLM processing

        Args:
            plant_name: Name of the plant to search for
            data_type: Type of data extraction ("organization", "plant", "unit")

        Returns:
            str: Formatted Excel data for LLM processing
        """
        try:
            if not self.dataset_loaded:
                self._load_dataset()

            if data_type == "organization":
                # Find the plant and get organization data
                plant_rows = self.usa_details[
                    self.usa_details['Plant Name'].str.contains(plant_name, case=False, na=False)
                ]

                if plant_rows.empty:
                    return f"Plant '{plant_name}' not found in USA dataset."

                # Get organization name
                org_name = plant_rows.iloc[0]['Entity Name']

                # Get all plants for this organization (limit to 50 for LLM context)
                org_plants = self.usa_details[
                    self.usa_details['Entity Name'] == org_name
                ].head(50)

                # Format as text table
                excel_text = f"USA Power Plant Dataset - Organization Data for: {org_name}\n"
                excel_text += "=" * 80 + "\n\n"
                excel_text += "Plant Name | Entity Name | Plant Type | State | Capacity (MW)\n"
                excel_text += "-" * 80 + "\n"

                for _, row in org_plants.iterrows():
                    excel_text += f"{row['Plant Name'][:30]:<30} | {row['Entity Name'][:20]:<20} | {str(row['Plant Type'])[:15]:<15} | {str(row['State']):<5} | {row['Nameplate Capacity (MW)']}\n"

                return excel_text

            elif data_type == "plant":
                # Get specific plant data with coordinates and units
                plant_rows = self.usa_details[
                    self.usa_details['Plant Name'].str.contains(plant_name, case=False, na=False)
                ]

                if plant_rows.empty:
                    return f"Plant '{plant_name}' not found in USA dataset."

                excel_text = f"USA Power Plant Dataset - Plant Data for: {plant_name}\n"
                excel_text += "=" * 80 + "\n\n"
                excel_text += "Plant Name | Latitude | Longitude | Plant Type | Capacity | Unit ID | State\n"
                excel_text += "-" * 80 + "\n"

                for _, row in plant_rows.iterrows():
                    excel_text += f"{row['Plant Name'][:25]:<25} | {row['Latitude']:<10} | {row['Longitude']:<10} | {str(row['Plant Type'])[:15]:<15} | {row['Nameplate Capacity (MW)']:<8} | {row['Unit ID']:<7} | {row['State']}\n"

                return excel_text

            elif data_type == "unit":
                # Get unit-level data with fuel consumption and generation data
                plant_rows = self.usa_details[
                    self.usa_details['Plant Name'].str.contains(plant_name, case=False, na=False)
                ]

                if plant_rows.empty:
                    return f"Plant '{plant_name}' not found in USA dataset."

                excel_text = f"USA Power Plant Dataset - Unit Data for: {plant_name}\n"
                excel_text += "=" * 80 + "\n\n"

                # Include USA Details data
                excel_text += "USA Details Sheet:\n"
                excel_text += "Unit ID | Plant Type | Capacity (MW) | Technology | Operating Month | Operating Year\n"
                excel_text += "-" * 80 + "\n"

                for _, row in plant_rows.iterrows():
                    excel_text += f"{row['Unit ID']:<7} | {str(row['Plant Type'])[:15]:<15} | {row['Nameplate Capacity (MW)']:<12} | {str(row['Technology'])[:15]:<15} | {str(row['Operating Month']):<15} | {row['Operating Year']}\n"

                # Include 2024 fuel and generation data if available
                if '2024' in self.yearly_sheets:
                    plant_2024_data = self.yearly_sheets['2024'][
                        self.yearly_sheets['2024']['Plant Name'].str.contains(plant_name, case=False, na=False)
                    ]

                    if not plant_2024_data.empty:
                        excel_text += "\n2024 Fuel Consumption & Generation Data:\n"
                        excel_text += "Plant Name | Fuel | Type | Electric Fuel Consumption | Net Generation | Emission Factor\n"
                        excel_text += "-" * 90 + "\n"

                        for _, row in plant_2024_data.iterrows():
                            excel_text += f"{row['Plant Name'][:20]:<20} | {str(row['Fuel']):<5} | {str(row.get('Type', '')):<5} | {row['Electric Fuel Consumption Quantity']:<25} | {row['Net Generation (Megawatthours)']:<15} | {row['Emission Factor']}\n"

                # Include Full Names mapping data for emission factor coal
                if 'Full Names' in self.yearly_sheets:
                    excel_text += "\nFull Names Mapping (for Emission Factor Coal):\n"
                    excel_text += "Code | Full Name | Emission Factor\n"
                    excel_text += "-" * 60 + "\n"

                    full_names_data = self.yearly_sheets['Full Names']
                    for _, row in full_names_data.iterrows():
                        excel_text += f"{str(row.get('Code', '')):<8} | {str(row.get('Full Name', ''))[:30]:<30} | {row.get('Emission Factor', '')}\n"

                return excel_text

        except Exception as e:
            return f"Error preparing Excel data: {e}"

    def extract_organization_data(self, plant_name: str, org_uid: str) -> Optional[Dict]:
        """
        Extract organization-level data using LLM analysis of USA dataset
        Uses provided org_uid instead of generating new one

        Args:
            plant_name: Name of the input plant
            org_uid: Organization UID from input (not generated)

        Returns:
            Dict: Organization-level JSON data with provided org_uid
        """
        try:
            print(f"🏢 LLM extracting organization data for plant: {plant_name}")

            # Prepare Excel data for LLM processing
            excel_data_text = self._prepare_excel_data_for_llm(plant_name, data_type="organization")

            # Generate organization JSON using LLM with Excel data
            org_prompt = f"""
You are analyzing USA power plant data from an Excel dataset. Based on the provided Excel data, extract organization-level information for the plant "{plant_name}".

Excel Data:
{excel_data_text}

Your task:
1. Find the organization (Entity Name) that owns the plant "{plant_name}"
2. Count all unique plants owned by this organization from the data provided
3. List all unique plant types for this organization
4. Generate organization-level JSON

Generate a JSON response with the following exact structure:
{{
    "cfpp_type": "Private",
    "country_name": "United States",
    "currency_in": "USD",
    "financial_year": "01-12",
    "organization_name": "extracted organization name",
    "plants_count": number_of_unique_plants,
    "plant_types": ["list", "of", "unique", "plant", "types"],
    "ppa_flag": "Plant-level",
    "province": "Various",
    "org_uid": "{org_uid}"
}}

Requirements:
- organization_name should be the exact Entity Name from the data
- plants_count should be the count of unique plant names for this organization
- plant_types should be a list of all unique plant types for this organization
- Only return valid JSON, no additional text or explanations
"""

            response = self.llm.invoke(org_prompt)

            # Parse JSON response
            try:
                org_data = self._parse_llm_json_response(response.content)
                # Ensure org_uid is set to the provided value
                org_data["org_uid"] = org_uid
                print(f"✅ LLM extracted organization data for {org_data.get('organization_name', 'Unknown')}")
                print(f"   Plants count: {org_data.get('plants_count', 0)}")
                print(f"   Plant types: {org_data.get('plant_types', [])}")
                print(f"   Using provided org_uid: {org_uid}")
                return org_data
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse LLM JSON response: {e}")
                print(f"Raw response: {response.content}")

                # Fallback: try to extract basic info manually
                if "not found" in excel_data_text.lower():
                    return None

                # Simple fallback extraction
                lines = excel_data_text.split('\n')
                org_name = "Unknown Organization"
                for line in lines:
                    if '|' in line and 'Entity Name' not in line and '---' not in line:
                        parts = line.split('|')
                        if len(parts) >= 2:
                            org_name = parts[1].strip()
                            break

                return {
                    "cfpp_type": "Private",
                    "country_name": "United States",
                    "currency_in": "USD",
                    "financial_year": "01-12",
                    "organization_name": org_name,
                    "plants_count": 1,
                    "plant_types": ["Unknown"],
                    "ppa_flag": "Plant-level",
                    "province": "Various",
                    "org_uid": org_uid
                }

        except Exception as e:
            print(f"❌ Error extracting organization data: {e}")
            return None

    def _get_entity_name_from_excel(self, plant_name: str) -> Optional[str]:
        """
        Get Entity Name from Excel data for the given plant

        Args:
            plant_name: Name of the power plant

        Returns:
            Entity Name from Excel or None if not found
        """
        try:
            plant_data = self.usa_details[
                self.usa_details['Plant Name'].str.contains(plant_name, case=False, na=False)
            ]

            if not plant_data.empty:
                entity_name = plant_data.iloc[0]['Entity Name']
                print(f"📋 Found Entity Name for {plant_name}: {entity_name}")
                return entity_name
            else:
                print(f"⚠️ No Excel data found for plant: {plant_name}")
                return None

        except Exception as e:
            print(f"❌ Error getting entity name: {e}")
            return None

    def _get_plants_under_entity(self, entity_name: str) -> List[str]:
        """
        Get all plants under the given entity from Excel data

        Args:
            entity_name: Name of the entity/organization

        Returns:
            List of plant names under this entity
        """
        try:
            entity_plants = self.usa_details[
                self.usa_details['Entity Name'].str.contains(entity_name, case=False, na=False)
            ]

            plant_names = entity_plants['Plant Name'].unique().tolist()
            print(f"🏭 Found {len(plant_names)} plants under entity '{entity_name}':")
            for plant in plant_names:
                print(f"   - {plant}")

            return plant_names

        except Exception as e:
            print(f"❌ Error getting plants under entity: {e}")
            return []

    def extract_plant_data(self, plant_name: str, org_uid: str) -> Optional[Dict]:
        """
        Extract plant-level data using LLM analysis of USA dataset
        Generates plant_uid and saves to database

        Args:
            plant_name: Name of the plant
            org_uid: Organization UID from input

        Returns:
            Dict: Plant-level JSON data with generated plant_uid
        """
        try:
            print(f"🏭 LLM extracting plant data for: {plant_name}")

            # Prepare Excel data for LLM processing
            excel_data_text = self._prepare_excel_data_for_llm(plant_name, data_type="plant")

            # Generate plant JSON using LLM with Excel data
            plant_prompt = f"""
You are analyzing USA power plant data from an Excel dataset. Based on the provided Excel data, extract plant-level information for "{plant_name}".

Excel Data:
{excel_data_text}

Your task:
1. Find the plant "{plant_name}" in the data
2. Extract latitude and longitude coordinates
3. Determine the plant type and map it to standard categories
4. Count the number of unique units (Unit IDs) for this plant
5. Generate plant-level JSON

Plant Type Mapping:
- "Conventional Steam Coal" → "coal"
- "Natural Gas Fired Combined Cycle" → "natural gas"
- "Natural Gas Fired Combustion Turbine" → "natural gas"
- "Natural Gas Internal Combustion Engine" → "natural gas"
- "Petroleum Liquids" → "petroleum liquids"
- "Onshore Wind Turbine" → "wind"
- "Solar Photovoltaic" → "solar"
- "Hydroelectric Conventional" → "hydro"
- Other types → use lowercase version

Generate a JSON response with the following exact structure:
{{
    "name": "{plant_name}",
    "plant_name": "{plant_name}",
    "state": "Unknown",
    "plant_id": 1,
    "plant_type": "mapped_plant_type",
    "lat": latitude_as_number,
    "long": longitude_as_number,
    "plant_address": "United States",
    "units_id": [1, 2, 3, ...],
    "ppa_details": [],
    "grid_connectivity_maps": [],
    "is_ppa": "yes",
    "is_retrofitting": "yes",
    "plant_transition_life": 5
}}

Requirements:
- lat and long should be numbers (not strings)
- plant_type should be mapped according to the rules above
- units_id should be a sequential list starting from 1, with length equal to number of unique Unit IDs
- Only return valid JSON, no additional text or explanations
"""

            response = self.llm.invoke(plant_prompt)

            # Parse JSON response
            import uuid
            try:
                plant_data = self._parse_llm_json_response(response.content)

                # Generate plant_uid and add to data
                plant_uid = str(uuid.uuid4())
                plant_data["plant_uid"] = plant_uid
                plant_data["org_uid"] = org_uid

                # Save plant_uid to database (you'll need to implement this)
                self._save_plant_to_database(plant_name, plant_uid, org_uid)

                # CRITICAL: Calculate plant-level emission factors AFTER plant extraction
                print(f"🧮 Calculating plant-level emission factors for: {plant_name}")
                plant_emission_data = self._calculate_plant_emission_factors(plant_name)
                emission_factor_coal_value = self._get_emission_factor_coal_value(plant_name)

                # Store these values for later use in unit extraction
                self._plant_emission_cache = {
                    'plant_name': plant_name,
                    'emission_factors': plant_emission_data,
                    'emission_factor_coal': emission_factor_coal_value
                }

                print(f"✅ Plant-level calculations complete:")
                print(f"   Emission factor coal: {emission_factor_coal_value}")
                print(f"   Emission factors for {len(plant_emission_data)} years")
                for ef in plant_emission_data:
                    print(f"      {ef.get('year')}: {ef.get('emission_factor')}")
                print(f"   Cached for plant: {plant_name}")

                print(f"✅ Plant-level calculations complete:")
                print(f"   Emission factor coal: {emission_factor_coal_value}")
                print(f"   Emission factors for {len(plant_emission_data)} years")

                # ADDITIONAL: Get Entity Name and discover organization plants
                print(f"🔍 Discovering organization plants using Entity Name...")
                entity_name = self._get_entity_name_from_excel(plant_name)

                if entity_name:
                    # Get all plants under this entity for organization discovery
                    entity_plants = self._get_plants_under_entity(entity_name)

                    # Store entity info for potential organization-level processing
                    plant_data["entity_name"] = entity_name
                    plant_data["entity_plants"] = entity_plants
                    plant_data["entity_plant_count"] = len(entity_plants)

                    print(f"📊 Organization Discovery Results:")
                    print(f"   Entity: {entity_name}")
                    print(f"   Total plants under entity: {len(entity_plants)}")
                else:
                    print(f"⚠️ Could not find Entity Name for organization discovery")

                print(f"✅ LLM extracted plant data for {plant_name}")
                print(f"   Generated plant_uid: {plant_uid}")
                print(f"   Using org_uid: {org_uid}")
                print(f"   Coordinates: ({plant_data.get('lat')}, {plant_data.get('long')})")
                print(f"   Plant type: {plant_data.get('plant_type')}")
                print(f"   Units: {plant_data.get('units_id', [])}")
                return plant_data
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse LLM JSON response: {e}")
                print(f"Raw response: {response.content}")

                # Fallback: try to extract basic info manually
                if "not found" in excel_data_text.lower():
                    return None

                # Simple fallback extraction
                lines = excel_data_text.split('\n')
                lat, lon, plant_type = None, None, "coal"
                unit_count = 1

                for line in lines:
                    if '|' in line and plant_name.lower() in line.lower():
                        parts = line.split('|')
                        if len(parts) >= 7:
                            try:
                                lat = float(parts[1].strip()) if parts[1].strip() else None
                                lon = float(parts[2].strip()) if parts[2].strip() else None
                                plant_type = parts[3].strip().lower() if parts[3].strip() else "coal"
                                break
                            except:
                                pass

                # Count units from data
                unit_lines = [line for line in lines if '|' in line and plant_name.lower() in line.lower()]
                unit_count = max(1, len(unit_lines))

                # Generate plant_uid for fallback case too
                import uuid
                plant_uid = str(uuid.uuid4())

                # Save plant_uid to database
                self._save_plant_to_database(plant_name, plant_uid, org_uid)

                return {
                    "name": plant_name,
                    "plant_name": plant_name,
                    "state": "Unknown",
                    "plant_id": 1,
                    "plant_type": plant_type,
                    "lat": lat,
                    "long": lon,
                    "plant_address": "United States",
                    "units_id": list(range(1, unit_count + 1)),
                    "ppa_details": [],
                    "grid_connectivity_maps": [],
                    "is_ppa": "yes",
                    "is_retrofitting": "yes",
                    "plant_transition_life": 5,
                    "plant_uid": plant_uid,
                    "org_uid": org_uid
                }

        except Exception as e:
            print(f"❌ Error extracting plant data: {e}")
            return None

    def _save_plant_to_database(self, plant_name: str, plant_uid: str, org_uid: str):
        """
        Save plant_uid to database for later retrieval
        Using in-memory cache for now
        """
        try:
            print(f"💾 Saving to database: plant_name={plant_name}, plant_uid={plant_uid}, org_uid={org_uid}")

            # Initialize cache if not exists
            if not hasattr(self, '_plant_uid_cache'):
                self._plant_uid_cache = {}

            # Store with exact plant name as key
            cache_key = plant_name.strip()
            self._plant_uid_cache[cache_key] = plant_uid
            print(f"✅ Cached plant_uid for '{cache_key}'")

        except Exception as e:
            print(f"⚠️ Error saving plant to database: {e}")

    def _get_plant_uid_from_database(self, plant_name: str) -> Optional[str]:
        """
        Retrieve plant_uid from database
        Using in-memory cache for now
        """
        try:
            print(f"🔍 Looking up plant_uid for: {plant_name}")

            # Check cache with exact plant name
            if hasattr(self, '_plant_uid_cache'):
                cache_key = plant_name.strip()
                plant_uid = self._plant_uid_cache.get(cache_key)
                if plant_uid:
                    print(f"✅ Found plant_uid: {plant_uid}")
                    return plant_uid
                else:
                    print(f"⚠️ Cache miss for '{cache_key}'. Available keys: {list(self._plant_uid_cache.keys())}")
            else:
                print(f"⚠️ No cache initialized yet")

            return None

        except Exception as e:
            print(f"⚠️ Error retrieving plant from database: {e}")
            return None

    def _map_units_to_sequential(self, units_raw: List) -> List[int]:
        """
        Map unit names to sequential numbers (1, 2, 3, etc.)
        This function reuses existing logic from the codebase
        """
        try:
            # Filter out NaN values and convert to list
            valid_units = [u for u in units_raw if pd.notna(u)]

            # Create sequential mapping
            sequential_units = list(range(1, len(valid_units) + 1))

            return sequential_units

        except Exception as e:
            print(f"⚠️ Error mapping units: {e}")
            return [1]  # Default to single unit

    def extract_unit_data(self, plant_name: str, unit_number: int, org_uid: str) -> Optional[Dict]:
        """
        Extract unit-level data using LLM analysis of USA dataset with plant-level emission factors
        Uses plant_uid from database for pk field

        Args:
            plant_name: Name of the plant
            unit_number: Unit number (1, 2, 3, etc.)
            org_uid: Organization UID from input

        Returns:
            Dict: Unit-level JSON data with plant_uid from database as pk
        """
        try:
            print(f"⚙️ LLM extracting unit {unit_number} data for: {plant_name}")

            # Prepare Excel data for LLM processing
            excel_data_text = self._prepare_excel_data_for_llm(plant_name, data_type="unit")

            # Generate unit JSON using LLM with Excel data
            unit_prompt = f"""
You are analyzing USA power plant data from an Excel dataset. Based on the provided Excel data, extract unit-level information for unit {unit_number} of plant "{plant_name}".

Excel Data:
{excel_data_text}

Your task:
1. Find unit {unit_number} data for plant "{plant_name}" (match by Unit ID = {unit_number})
2. Extract capacity, fuel type, technology, and operational dates
3. Generate unit-level JSON (DO NOT include emission_factor or emission_factor_coal - these will be added separately)

Fuel Type Mapping:
- "COL" → "coal"
- "NG" → "natural gas"
- "DFO" → "petroleum liquids"
- "BIO" → "biomass"
- "WND" → "wind"
- "SUN" → "solar"
- Other → use lowercase version

Generate a JSON response with the following exact structure:
{{
    "sk": "unit#plant_type#{unit_number}#plant#1",
    "unit_number": {unit_number},
    "plant_id": 1,
    "capacity": capacity_in_mw,
    "capacity_unit": "MW",
    "technology": "extracted_technology_or_Sub-critical",
    "boiler_type": "Pulverized Coal",
    "commencement_date": "yyyy-mm-ddThh:mm:ss.msZ",
    "remaining_useful_life": "yyyy-mm-ddThh:mm:ss.msZ",
    "unit_lifetime": calculated_lifetime_years,
    "plf": [],
    "PAF": [],
    "auxiliary_power_consumed": [],
    "gross_power_generation": [],
    "coal_unit_efficiency": 0.35,
    "heat_rate": 2456.71,
    "heat_rate_unit": "kJ/kWh",
    "fuel_type": [{{"fuel": "mapped_fuel_type", "plant_type": "mapped_plant_type"}}],
    "gcv_coal": 6690,
    "gcv_coal_unit": "kCal/kg"
}}

Requirements:
- sk format: unit#plant_type#unit_number#plant#1
- capacity should be a number in MW
- fuel should be mapped according to the rules above
- commencement_date should be ISO format based on Operating Month/Year
- fuel_type should be an array with one object containing fuel and plant_type
- DO NOT include emission_factor or emission_factor_coal fields in your response
- Only return valid JSON, no additional text or explanations
"""

            response = self.llm.invoke(unit_prompt)

            # Parse JSON response
            try:
                print(f"🤖 Raw LLM response for unit {unit_number}:")
                print(f"   Response length: {len(response.content)} characters")
                print(f"   First 200 chars: {response.content[:200]}...")

                unit_data = self._parse_llm_json_response(response.content)

                # Check if LLM included emission factors (it shouldn't) and remove them
                if 'emission_factor' in unit_data:
                    print(f"⚠️ WARNING: LLM included emission_factor field (removing)")
                    print(f"   LLM emission_factor: {unit_data['emission_factor']}")
                    del unit_data['emission_factor']
                if 'emission_factor_coal' in unit_data:
                    print(f"⚠️ WARNING: LLM included emission_factor_coal field (removing)")
                    print(f"   LLM emission_factor_coal: {unit_data['emission_factor_coal']}")
                    del unit_data['emission_factor_coal']

                print(f"✅ Unit data parsed from LLM (emission factors removed if present)")

                # Get plant_uid from database for pk field
                plant_uid = self._get_plant_uid_from_database(plant_name)
                if plant_uid:
                    unit_data["pk"] = plant_uid
                    unit_data["plant_uid"] = plant_uid
                else:
                    print(f"⚠️ Warning: Could not find plant_uid for {plant_name} in database")
                    # Generate a new one as fallback
                    import uuid
                    plant_uid = str(uuid.uuid4())
                    unit_data["pk"] = plant_uid
                    unit_data["plant_uid"] = plant_uid
                    self._save_plant_to_database(plant_name, plant_uid, org_uid)

                # Fix commencement_date and remaining_useful_life using Excel data
                print(f"🗓️ Fixing dates for unit {unit_number}")
                try:
                    # Get unit data from USA Details sheet - match by plant name AND unit number
                    # First, let's see what columns are available
                    print(f"   📋 Available columns: {list(self.usa_details.columns)}")

                    # Try different possible column names for unit number
                    unit_col = None
                    for possible_col in ['Unit IDs', 'Unit ID', 'Unit Number', 'Unit', 'unit_id', 'unit_number']:
                        if possible_col in self.usa_details.columns:
                            unit_col = possible_col
                            break

                    if unit_col:
                        print(f"   🔍 Using unit column: {unit_col}")
                        plant_data = self.usa_details[
                            (self.usa_details['Plant Name'].str.contains(plant_name, case=False, na=False)) &
                            (self.usa_details[unit_col] == unit_number)
                        ]
                    else:
                        print(f"   ⚠️ No unit column found, using plant name only")
                        plant_data = self.usa_details[
                            self.usa_details['Plant Name'].str.contains(plant_name, case=False, na=False)
                        ]

                    if not plant_data.empty:
                        print(f"   📊 Found {len(plant_data)} matching rows")

                        # If we have unit column matching, use first row
                        # If no unit column, use the unit_number-th row (1-based indexing)
                        if unit_col:
                            unit_row = plant_data.iloc[0]
                            print(f"   🎯 Using exact unit match for unit {unit_number}")
                        else:
                            if len(plant_data) >= unit_number:
                                unit_row = plant_data.iloc[unit_number - 1]  # 1-based to 0-based
                                print(f"   🎯 Using row {unit_number} for unit {unit_number}")
                            else:
                                print(f"   ⚠️ Not enough rows ({len(plant_data)}) for unit {unit_number}")
                                unit_row = plant_data.iloc[0]  # Use first available

                        print(f"   📊 Excel data for unit {unit_number}:")
                        print(f"      Operating Month: {unit_row.get('Operating Month', 'N/A')}")
                        print(f"      Operating Year: {unit_row.get('Operating Year', 'N/A')}")
                        print(f"      Planned Retirement Month: {unit_row.get('Planned Retirement Month', 'N/A')}")
                        print(f"      Planned Retirement Year: {unit_row.get('Planned Retirement Year', 'N/A')}")

                        # Format commencement date from Operating Month/Year
                        commencement_date = self._format_commencement_date(unit_row)
                        unit_data["commencement_date"] = commencement_date

                        # Format remaining useful life (add 50 years if not found)
                        remaining_useful_life = self._format_remaining_useful_life(unit_row, commencement_date)
                        unit_data["remaining_useful_life"] = remaining_useful_life

                        # Calculate unit lifetime
                        unit_lifetime = self._calculate_unit_lifetime(commencement_date, remaining_useful_life)
                        unit_data["unit_lifetime"] = unit_lifetime

                        print(f"   ✅ Commencement date: {commencement_date}")
                        print(f"   ✅ Remaining useful life: {remaining_useful_life}")
                        print(f"   ✅ Unit lifetime: {unit_lifetime} years")
                    else:
                        print(f"   ⚠️ No data found for {plant_name} in Excel")
                        # Show available plants for debugging
                        available_plants = self.usa_details['Plant Name'].unique()[:5]  # First 5
                        print(f"   📊 Sample available plants: {list(available_plants)}")
                except Exception as e:
                    print(f"   ❌ Error fixing dates: {e}")
                    import traceback
                    traceback.print_exc()
                    # Keep LLM-generated dates as fallback

                # CRITICAL: Add plant-level emission factors (same for all units)
                print(f"🔍 Checking emission cache for unit {unit_number} of {plant_name}")
                print(f"   Cache exists: {hasattr(self, '_plant_emission_cache')}")
                if hasattr(self, '_plant_emission_cache'):
                    print(f"   Cached plant name: {self._plant_emission_cache.get('plant_name')}")
                    print(f"   Target plant name: {plant_name}")
                    print(f"   Names match: {self._plant_emission_cache.get('plant_name') == plant_name}")

                if hasattr(self, '_plant_emission_cache') and self._plant_emission_cache.get('plant_name') == plant_name:
                    # Use cached plant-level emission data
                    cached_emission_factors = self._plant_emission_cache.get('emission_factors', [])
                    cached_emission_factor_coal = self._plant_emission_cache.get('emission_factor_coal', "2.44")

                    print(f"✅ Using cached emission data:")
                    print(f"   Emission factor coal: {cached_emission_factor_coal}")
                    print(f"   Emission factors count: {len(cached_emission_factors)}")
                    for ef_data in cached_emission_factors:
                        print(f"      {ef_data.get('year')}: {ef_data.get('emission_factor')}")

                    # Format emission factors for JSON
                    emission_factor_array = []
                    for ef_data in cached_emission_factors:
                        emission_factor_array.append({
                            "value": ef_data.get("emission_factor", 0.0),
                            "year": ef_data.get("year", 2024)
                        })

                    unit_data["emission_factor"] = emission_factor_array
                    unit_data["emission_factor_coal"] = cached_emission_factor_coal

                    print(f"✅ Applied plant-level emission data to unit {unit_number}")
                    print(f"   Final emission_factor_coal: {unit_data['emission_factor_coal']}")
                    print(f"   Final emission_factor count: {len(unit_data['emission_factor'])}")
                    print(f"   Final emission_factor values:")
                    for ef in unit_data['emission_factor']:
                        print(f"      {ef.get('year')}: {ef.get('value')}")
                else:
                    print(f"⚠️ Warning: No cached emission data found for {plant_name}")
                    # Fallback values
                    unit_data["emission_factor"] = []
                    unit_data["emission_factor_coal"] = "100 kg CO2 eq/TJ (IPCC Guidelines for Energy)"

                # FINAL VALIDATION: Ensure emission factors are from cache only
                print(f"🔒 Final emission factor validation for unit {unit_number}")
                print(f"   Cache exists: {hasattr(self, '_plant_emission_cache')}")

                if hasattr(self, '_plant_emission_cache'):
                    cache = self._plant_emission_cache
                    print(f"   Cache plant name: {cache.get('plant_name')}")
                    print(f"   Target plant name: {plant_name}")
                    print(f"   Names match: {cache.get('plant_name') == plant_name}")
                    print(f"   Cache emission_factor_coal: {cache.get('emission_factor_coal')}")
                    print(f"   Cache emission_factors count: {len(cache.get('emission_factors', []))}")

                if hasattr(self, '_plant_emission_cache') and self._plant_emission_cache.get('plant_name') == plant_name:
                    cached_emission_factors = self._plant_emission_cache.get('emission_factors', [])
                    cached_emission_factor_coal = self._plant_emission_cache.get('emission_factor_coal', "2.44")

                    print(f"   🔄 Forcing cached values:")
                    print(f"      Cached coal value: {cached_emission_factor_coal}")
                    print(f"      Cached factors: {len(cached_emission_factors)} items")

                    # Force override with cached values (final safety check)
                    emission_factor_array = []
                    for ef_data in cached_emission_factors:
                        emission_factor_array.append({
                            "value": ef_data.get("emission_factor", 0.0),
                            "year": ef_data.get("year", 2024)
                        })

                    unit_data["emission_factor"] = emission_factor_array
                    unit_data["emission_factor_coal"] = cached_emission_factor_coal

                    print(f"   ✅ FINAL emission_factor_coal: {unit_data['emission_factor_coal']}")
                    print(f"   ✅ FINAL emission_factor count: {len(unit_data['emission_factor'])}")
                else:
                    print(f"   ❌ Cache not found or plant name mismatch - using defaults")
                    unit_data["emission_factor"] = []
                    unit_data["emission_factor_coal"] = "100 kg CO2 eq/TJ (IPCC Guidelines for Energy)"

                # ABSOLUTE FINAL VALIDATION: Force cached emission factors one more time
                print(f"🔒 ABSOLUTE FINAL validation before return:")
                if hasattr(self, '_plant_emission_cache') and self._plant_emission_cache.get('plant_name') == plant_name:
                    cached_emission_factors = self._plant_emission_cache.get('emission_factors', [])
                    cached_emission_factor_coal = self._plant_emission_cache.get('emission_factor_coal', "2.44")

                    # Force override emission factors (final safety net)
                    emission_factor_array = []
                    for ef_data in cached_emission_factors:
                        emission_factor_array.append({
                            "value": ef_data.get("emission_factor", 0.0),
                            "year": ef_data.get("year", 2024)
                        })

                    unit_data["emission_factor"] = emission_factor_array
                    unit_data["emission_factor_coal"] = cached_emission_factor_coal

                    print(f"   🔒 FORCED emission_factor_coal: {unit_data['emission_factor_coal']}")
                    print(f"   🔒 FORCED emission_factor count: {len(unit_data['emission_factor'])}")
                else:
                    print(f"   ⚠️ No cache found - using defaults")
                    unit_data["emission_factor"] = []
                    unit_data["emission_factor_coal"] = "2.44"

                # FINAL DATE VALIDATION: Check if dates are still default values
                current_commencement = unit_data.get("commencement_date", "")
                if current_commencement == "2000-01-01T00:00:00.000Z":
                    print(f"   ⚠️ WARNING: Commencement date is still default value")
                    print(f"   🔄 Attempting to re-extract date from Excel...")

                    # Try to re-extract the date one more time
                    try:
                        unit_col = 'Unit IDs' if 'Unit IDs' in self.usa_details.columns else None
                        if unit_col:
                            plant_data = self.usa_details[
                                (self.usa_details['Plant Name'].str.contains(plant_name, case=False, na=False)) &
                                (self.usa_details[unit_col] == unit_number)
                            ]
                            if not plant_data.empty:
                                unit_row = plant_data.iloc[0]
                                fixed_date = self._format_commencement_date(unit_row)
                                unit_data["commencement_date"] = fixed_date
                                print(f"   ✅ FIXED commencement date: {fixed_date}")
                    except Exception as e:
                        print(f"   ❌ Could not fix date: {e}")
                else:
                    print(f"   ✅ Commencement date looks good: {current_commencement}")

                print(f"✅ LLM extracted unit {unit_number} data for {plant_name}")
                print(f"   Using plant_uid: {plant_uid}")
                print(f"   Capacity: {unit_data.get('capacity', 'N/A')} MW")
                print(f"   Fuel type: {unit_data.get('fuel_type', [{}])[0].get('fuel', 'N/A') if unit_data.get('fuel_type') else 'N/A'}")
                print(f"   Technology: {unit_data.get('technology', 'N/A')}")
                print(f"   Emission factors: {len(unit_data.get('emission_factor', []))} data points")
                print(f"   Emission factor coal: {unit_data.get('emission_factor_coal', 'N/A')}")

                return unit_data
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse LLM JSON response: {e}")
                print(f"Raw response: {response.content}")

                # Fallback: try to extract basic info manually
                if "not found" in excel_data_text.lower():
                    return None

                # Simple fallback extraction
                # Get plant_uid from database for fallback case too
                plant_uid = self._get_plant_uid_from_database(plant_name)
                if not plant_uid:
                    import uuid
                    plant_uid = str(uuid.uuid4())
                    self._save_plant_to_database(plant_name, plant_uid, org_uid)

                # Get cached emission data for fallback too
                emission_factor_array = []
                emission_factor_coal_value = "2.44"

                if hasattr(self, '_plant_emission_cache') and self._plant_emission_cache.get('plant_name') == plant_name:
                    cached_emission_factors = self._plant_emission_cache.get('emission_factors', [])
                    cached_emission_factor_coal = self._plant_emission_cache.get('emission_factor_coal', emission_factor_coal_value)

                    for ef_data in cached_emission_factors:
                        emission_factor_array.append({
                            "value": ef_data.get("emission_factor", 0.0),
                            "year": ef_data.get("year", 2024)
                        })
                    emission_factor_coal_value = cached_emission_factor_coal

                return {
                    "sk": f"unit#coal#{unit_number}#plant#1",
                    "unit_number": unit_number,
                    "plant_id": 1,
                    "pk": plant_uid,
                    "plant_uid": plant_uid,
                    "capacity": 100.0,  # Default capacity
                    "capacity_unit": "MW",
                    "technology": "Sub-critical",
                    "boiler_type": "Pulverized Coal",
                    "commencement_date": "2000-01-01T00:00:00.000Z",
                    "remaining_useful_life": "2050-01-01T00:00:00.000Z",
                    "unit_lifetime": 50,
                    "plf": [],
                    "PAF": [],
                    "auxiliary_power_consumed": [],
                    "gross_power_generation": [],
                    "emission_factor": emission_factor_array,
                    "emission_factor_coal": emission_factor_coal_value,
                    "coal_unit_efficiency": 0.35,
                    "heat_rate": 2456.71,
                    "heat_rate_unit": "kJ/kWh",
                    "fuel_type": [{"fuel": "coal", "plant_type": "coal"}],
                    "gcv_coal": 6690,
                    "gcv_coal_unit": "kCal/kg"
                }

        except Exception as e:
            print(f"❌ Error extracting unit data: {e}")
            return None

    def _calculate_plant_emission_factors(self, plant_name: str) -> List[Dict]:
        """
        Calculate emission factors for all 5 years (2020-2024) for the entire plant using LLM
        Only for coal-based plants (Fuel = "COL")
        Formula: emission_factor = (A / B) * C where:
        A = Electric Fuel Consumption Quantity * 0.9072
        B = Net Generation (Megawatthours)
        C = emission_factor_coal
        """
        try:
            print(f"🧮 Calculating plant-level emission factors for: {plant_name}")
            print(f"   📊 Using separated yearly sheets: usa_database/2020.xlsx to usa_database/2024.xlsx")

            if not self.dataset_loaded:
                self._load_dataset()

            # Store results in dummy variable as requested
            calculated_emission_factors_list = []

            # Get emission_factor_coal value first and store in dummy variable
            emission_factor_coal_str = self._get_emission_factor_coal_value(plant_name)

            # Extract numeric value from emission_factor_coal and store in dummy variable
            import re
            numeric_match = re.search(r'(\d+\.?\d*)', emission_factor_coal_str)
            coal_emission_factor_C = float(numeric_match.group(1)) if numeric_match else 100.0

            print(f"   Using emission_factor_coal (C): {coal_emission_factor_C}")

            # Process each year separately using individual sheet files
            for year in range(2020, 2025):
                year_str = str(year)
                print(f"   📊 Processing {year_str} from usa_database/{year_str}.xlsx")

                if year_str not in self.yearly_sheets:
                    print(f"   ⚠️ {year_str} data not available from usa_database/{year_str}.xlsx, skipping")
                    continue

                # Get data from specific year sheet and store in dummy variable
                yearly_sheet_data = self.yearly_sheets[year_str]
                print(f"   📋 {year_str} sheet has {len(yearly_sheet_data)} rows")

                # Find plant data for this year and store in dummy variable
                plant_data_for_year = yearly_sheet_data[
                    yearly_sheet_data['Plant Name'].str.contains(plant_name, case=False, na=False)
                ]

                if plant_data_for_year.empty:
                    print(f"   ⚠️ No data found for {plant_name} in {year_str}")
                    continue

                # Filter only coal units (Fuel = "COL") and store in dummy variable
                coal_units_for_year = plant_data_for_year[plant_data_for_year['Fuel'] == 'COL']

                if coal_units_for_year.empty:
                    print(f"   ⚠️ No coal units found for {plant_name} in {year_str}")
                    continue

                # Convert to text for LLM processing and store in dummy variable
                year_data_text = coal_units_for_year.to_string(index=False)

                # Create LLM prompt for this specific year
                llm_prompt_for_year = f"""
You are analyzing power plant data from usa_database/{year_str}.xlsx sheet.

TASK: Calculate emission factor for plant "{plant_name}" for year {year_str}

FORMULA: emission_factor = (A / B) * C
Where:
- A = Electric Fuel Consumption Quantity * 0.9072
- B = Net Generation (Megawatthours)
- C = {coal_emission_factor_C} (emission_factor_coal value)

DATA FROM usa_database/{year_str}.xlsx (Coal units only):
{year_data_text}

STEP-BY-STEP INSTRUCTIONS:
1. Find all rows where Plant Name contains "{plant_name}" AND Fuel = "COL"
2. Sum all "Electric Fuel Consumption Quantity" values for these coal units
3. Sum all "Net Generation (Megawatthours)" values for these coal units
4. Calculate A = Total Electric Fuel Consumption * 0.9072
5. Calculate B = Total Net Generation
6. Calculate emission_factor = (A / B) * {coal_emission_factor_C}

IMPORTANT RULES:
- Only include coal units (Fuel = "COL")
- Sum values across ALL coal units of the plant
- Use exact column names from the data
- Return only the final calculated number

RESPONSE FORMAT: Return only the calculated emission_factor number, no text.
Example: "0.85"
"""

                # Get LLM response for this year and store in dummy variable
                llm_response = self.llm.invoke(llm_prompt_for_year)
                llm_emission_factor_response = llm_response.content.strip()

                print(f"   🤖 LLM response for {year_str}: {llm_emission_factor_response}")

                # Extract numeric value and store in dummy variable
                try:
                    yearly_emission_factor_value = float(llm_emission_factor_response.replace('"', ''))

                    # Store in dummy variable structure for this year
                    year_emission_data = {
                        "year": int(year_str),
                        "emission_factor": yearly_emission_factor_value
                    }
                    calculated_emission_factors_list.append(year_emission_data)

                    print(f"   ✅ {year_str}: EF = {yearly_emission_factor_value}")
                except ValueError:
                    print(f"   ❌ Could not parse emission factor for {year_str}: {llm_emission_factor_response}")

            # Final validation of calculated results
            if calculated_emission_factors_list:
                print(f"✅ Calculated {len(calculated_emission_factors_list)} emission factor data points for {plant_name}")
                for ef_data in calculated_emission_factors_list:
                    year = ef_data.get('year', 'Unknown')
                    ef_value = ef_data.get('emission_factor', 0)
                    print(f"   ✅ {year}: EF = {ef_value}")

                # Return the dummy variable containing all calculated values
                return calculated_emission_factors_list
            else:
                print(f"❌ No valid emission factors calculated")
                return []

        except Exception as e:
            print(f"❌ Error calculating plant emission factors: {e}")
            return []

    def _get_emission_factor_coal_value(self, plant_name: str) -> str:
        """
        Get emission_factor_coal value using FIXED mapping based on fuel type from 2024.xlsx sheet
        Plant-level determination - same value for all units of the plant

        Uses separated sheet: usa_database/2024.xlsx

        Fixed mapping:
        ANT - 2.62
        SUB - 1.82
        LIG - 1.20
        BIT - 2.44

        Args:
            plant_name: Name of the plant

        Returns:
            Fixed emission factor coal value as string (numeric only)
        """
        try:
            print(f"🔍 Getting emission factor coal for plant: {plant_name}")
            print(f"   📊 Using sheet: usa_database/2024.xlsx")

            if not self.dataset_loaded:
                self._load_dataset()

            # Get 2024 data from usa_database/2024.xlsx
            if '2024' not in self.yearly_sheets:
                print(f"❌ 2024 data not available from usa_database/2024.xlsx, using default BIT value")
                return "2.44"

            year_2024 = self.yearly_sheets['2024']
            print(f"   📋 2024 sheet loaded with {len(year_2024)} rows")

            # Ask LLM to find plant data and extract fuel type code
            plant_data_text = year_2024.to_string(index=False)

            llm_prompt = f"""
You are analyzing power plant data from usa_database/2024.xlsx sheet.

Your task:
1. Find plant "{plant_name}" in the data below
2. Look for coal units (where Fuel = "COL")
3. Extract the "Type" column value (fuel type code like BIT, SUB, ANT, LIG)
4. This is PLANT-LEVEL data - all units of the same plant have the same fuel type

2024 SHEET DATA:
{plant_data_text}

INSTRUCTIONS:
- Find rows where Plant Name contains "{plant_name}" (case insensitive)
- Filter for coal units only (Fuel = "COL")
- Extract the Type column value (should be same for all coal units of this plant)
- Return only the fuel type code (BIT, SUB, ANT, LIG, etc.)

RESPONSE FORMAT: Return only the fuel type code, nothing else.
Example: "BIT"
"""

            # Get LLM response for fuel type extraction
            response = self.llm.invoke(llm_prompt)
            fuel_type_code = response.content.strip().upper().replace('"', '')

            print(f"   🤖 LLM extracted fuel type code: '{fuel_type_code}'")

            # Store in dummy variable as requested
            extracted_fuel_type_code = fuel_type_code

            # Fixed mapping based on fuel type codes
            emission_factor_mapping = {
                'ANT': '2.62',  # Anthracite
                'SUB': '1.82',  # Subbituminous
                'LIG': '1.20',  # Lignite
                'BIT': '2.44'   # Bituminous (default)
            }

            # Get the fixed value based on extracted fuel type code
            if extracted_fuel_type_code in emission_factor_mapping:
                emission_value = emission_factor_mapping[extracted_fuel_type_code]
                print(f"   ✅ Mapped {extracted_fuel_type_code} → {emission_value}")

                # Store final value in dummy variable
                final_emission_factor_coal = emission_value
                return final_emission_factor_coal
            else:
                print(f"   ⚠️ Unknown fuel type code '{extracted_fuel_type_code}', using default BIT value")
                final_emission_factor_coal = "2.44"  # Default to BIT
                return final_emission_factor_coal

        except Exception as e:
            print(f"❌ Error getting emission factor coal: {e}")
            print("   Using default BIT value")
            final_emission_factor_coal = "2.44"
            return final_emission_factor_coal



    def _extract_unit_capacity(self, plant_name: str, unit_number: int) -> Optional[float]:
        """Extract capacity for specific unit from USA Details sheet"""
        try:
            plant_data = self.usa_details[
                self.usa_details['Plant Name'].str.contains(plant_name, case=False, na=False)
            ]

            if plant_data.empty:
                return None

            # Get capacity for the specific unit
            unit_data = plant_data[plant_data['Unit IDs'] == unit_number]

            if not unit_data.empty:
                capacity = unit_data.iloc[0]['Capacity']
                return float(capacity) if pd.notna(capacity) else None
            else:
                # If specific unit not found, use average capacity
                total_capacity = plant_data['Capacity'].sum()
                unit_count = len(plant_data)
                return total_capacity / unit_count if unit_count > 0 else None

        except Exception as e:
            print(f"⚠️ Error extracting unit capacity: {e}")
            return None

    def _extract_fuel_type(self, plant_name: str, unit_number: int) -> Dict:
        """Extract fuel type data from 2024 sheet"""
        try:
            if '2024' not in self.yearly_sheets:
                return {"fuel": "coal", "type": "bituminous"}

            sheet_2024 = self.yearly_sheets['2024']

            # Find plant data in 2024 sheet
            plant_data = sheet_2024[
                sheet_2024['Plant Name'].str.contains(plant_name, case=False, na=False)
            ]

            if plant_data.empty:
                return {"fuel": "coal", "type": "bituminous"}

            # Get fuel and type from first matching row
            row = plant_data.iloc[0]
            fuel = row.get('Fuel', 'COL')  # Default to coal
            fuel_type = row.get('Type', 'BIT')  # Default to bituminous

            # Map fuel codes to full names
            fuel_mapping = {
                'COL': 'coal',
                'NG': 'natural gas',
                'BIO': 'biomass',
                'OIL': 'oil'
            }

            type_mapping = {
                'BIT': 'bituminous',
                'SUB': 'sub-bituminous',
                'LIG': 'lignite',
                'ANT': 'anthracite'
            }

            return {
                "fuel": fuel_mapping.get(fuel, fuel.lower()),
                "type": type_mapping.get(fuel_type, fuel_type.lower()),
                "plant_type": fuel_mapping.get(fuel, 'coal')
            }

        except Exception as e:
            print(f"⚠️ Error extracting fuel type: {e}")
            return {"fuel": "coal", "type": "bituminous", "plant_type": "coal"}



    def _format_commencement_date(self, plant_row) -> str:
        """Format commencement date as ISO timestamp"""
        try:
            operating_month = plant_row.get('Operating Month')
            operating_year = plant_row.get('Operating Year')

            if pd.notna(operating_month) and pd.notna(operating_year):
                # Create datetime object
                month = int(operating_month) if operating_month != 0 else 1
                year = int(operating_year)

                # Format as ISO timestamp
                dt = datetime(year, month, 1, 0, 0, 0)
                return dt.strftime("%Y-%m-%dT%H:%M:%S.%fZ")[:-3] + "Z"
            else:
                # Default to 1990-01-01 if no data
                return "1990-01-01T00:00:00.000Z"

        except Exception as e:
            print(f"⚠️ Error formatting commencement date: {e}")
            return "1990-01-01T00:00:00.000Z"

    def _format_remaining_useful_life(self, plant_row, commencement_date: str) -> str:
        """Format remaining useful life as ISO timestamp"""
        try:
            retirement_month = plant_row.get('Planned Retirement Month')
            retirement_year = plant_row.get('Planned Retirement Year')

            if pd.notna(retirement_month) and pd.notna(retirement_year):
                # Use planned retirement date
                month = int(retirement_month) if retirement_month != 0 else 12
                year = int(retirement_year)

                dt = datetime(year, month, 1, 0, 0, 0)
                return dt.strftime("%Y-%m-%dT%H:%M:%S.%fZ")[:-3] + "Z"
            else:
                # Add 50 years to commencement date
                try:
                    comm_dt = datetime.strptime(commencement_date[:19], "%Y-%m-%dT%H:%M:%S")
                    retirement_dt = comm_dt.replace(year=comm_dt.year + 50)
                    return retirement_dt.strftime("%Y-%m-%dT%H:%M:%S.%fZ")[:-3] + "Z"
                except:
                    return "2040-01-01T00:00:00.000Z"

        except Exception as e:
            print(f"⚠️ Error formatting remaining useful life: {e}")
            return "2040-01-01T00:00:00.000Z"

    def _calculate_unit_lifetime(self, commencement_date: str, remaining_useful_life: str) -> float:
        """Calculate unit lifetime in years"""
        try:
            comm_dt = datetime.strptime(commencement_date[:19], "%Y-%m-%dT%H:%M:%S")
            retire_dt = datetime.strptime(remaining_useful_life[:19], "%Y-%m-%dT%H:%M:%S")

            lifetime_years = (retire_dt - comm_dt).days / 365.25
            return round(lifetime_years, 1)

        except Exception as e:
            print(f"⚠️ Error calculating unit lifetime: {e}")
            return 50.0  # Default 50 years

    def _map_technology(self, fuel: str) -> str:
        """Map fuel type to technology description"""
        mapping = {
            'coal': 'Sub-critical',
            'natural gas': 'Combined Cycle',
            'biomass': 'Direct Combustion',
            'oil': 'Steam Turbine'
        }
        return mapping.get(fuel.lower(), 'Sub-critical')

    def process_usa_plant(self, plant_name: str, org_uid: str) -> Dict[str, Any]:
        """
        Main method to process a USA plant through the complete pipeline

        Args:
            plant_name: Name of the power plant
            org_uid: Organization UID from input (not generated)

        Returns:
            Dict containing all extracted data (organization, plant, units)
        """
        try:
            print(f"🇺🇸 Processing USA plant: {plant_name}")

            # Step 1: Extract organization data
            org_data = self.extract_organization_data(plant_name, org_uid)
            if not org_data:
                return {"success": False, "error": "Failed to extract organization data"}

            # Step 2: Extract plant data
            plant_data = self.extract_plant_data(plant_name, org_uid)
            if not plant_data:
                return {"success": False, "error": "Failed to extract plant data"}

            # Step 3: Extract unit data for all units
            units_data = []
            for unit_num in plant_data.get('units_id', [1]):
                unit_data = self.extract_unit_data(plant_name, unit_num, org_uid)
                if unit_data:
                    units_data.append(unit_data)

            if not units_data:
                return {"success": False, "error": "Failed to extract unit data"}

            result = {
                "success": True,
                "source": "usa_dataset",
                "organization": org_data,
                "plant": plant_data,
                "units": units_data,
                "plant_name": plant_name,
                "country": "United States"
            }

            print(f"✅ Successfully processed USA plant {plant_name}")
            print(f"   Organization: {org_data['organization_name']}")
            print(f"   Plant type: {plant_data['plant_type']}")
            print(f"   Units: {len(units_data)}")

            return result

        except Exception as e:
            print(f"❌ Error processing USA plant {plant_name}: {e}")
            return {"success": False, "error": str(e)}


# Global instance for easy access
usa_pipeline = USADatasetPipeline()


def detect_plant_country(plant_name: str, organization_name: str = None) -> str:
    """
    Detect the country of a power plant

    Args:
        plant_name: Name of the power plant
        organization_name: Optional organization name

    Returns:
        str: Country name ("United States" or "Unknown")
    """
    try:
        if usa_pipeline.is_usa_plant(plant_name, organization_name):
            return "United States"
        else:
            return "Unknown"
    except Exception as e:
        print(f"⚠️ Error detecting plant country: {e}")
        return "Unknown"


def process_plant_with_country_routing(plant_name: str, org_uid: str, organization_name: str = None) -> Dict[str, Any]:
    """
    Main routing function that determines whether to use USA pipeline or fallback to web search

    Args:
        plant_name: Name of the power plant
        org_uid: Organization UID from input (not generated)
        organization_name: Optional organization name

    Returns:
        Dict containing extraction results or routing information
    """
    try:
        print(f"🔍 Determining processing route for: {plant_name}")

        # Step 1: Detect country
        country = detect_plant_country(plant_name, organization_name)

        if country == "United States":
            print(f"🇺🇸 Routing to USA dataset pipeline")
            return usa_pipeline.process_usa_plant(plant_name, org_uid)
        else:
            print(f"🌐 Routing to web search pipeline (non-USA plant)")
            return {
                "success": False,
                "route_to_web_search": True,
                "country": "Unknown",
                "message": "Plant not found in USA dataset, use web search pipeline"
            }

    except Exception as e:
        print(f"❌ Error in country routing: {e}")
        return {
            "success": False,
            "route_to_web_search": True,
            "error": str(e),
            "message": "Error in routing, fallback to web search"
        }
