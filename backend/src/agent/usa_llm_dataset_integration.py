#!/usr/bin/env python3

import pandas as pd
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import re
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.schema import HumanMessage

# Set up logging
logger = logging.getLogger(__name__)

class USALLMDatasetIntegration:
    """USA Dataset Integration using LLM for data extraction and calculations"""
    
    def __init__(self, excel_file_path: str = "USA Details.xlsx"):
        # CRITICAL FIX: Try multiple possible paths for the Excel file
        possible_paths = [
            "USA Details.xlsx",  # Direct path (when running from backend directory)
            "../USA Details.xlsx",  # When running from backend/src directory
            "backend/USA Details.xlsx",  # When running from root directory
            "../../USA Details.xlsx",  # Fallback path
        ]

        self.excel_file_path = None
        for path in possible_paths:
            if os.path.exists(path):
                self.excel_file_path = path
                logger.info(f"✅ Found USA dataset at: {path}")
                break

        if not self.excel_file_path:
            self.excel_file_path = excel_file_path  # Use default if none found
            logger.error(f"❌ USA Details.xlsx not found in any of these paths: {possible_paths}")
            logger.error(f"❌ Current working directory: {os.getcwd()}")

        self.excel_data = {}
        self.loaded = False
        
        # Initialize LLM with API key from environment
        try:
            import os
            from dotenv import load_dotenv

            # Load environment variables from backend/.env
            load_dotenv('../.env')  # Updated path
            load_dotenv('backend/.env')  # Fallback path

            api_key = os.getenv('GEMINI_API_KEY')
            if not api_key:
                logger.warning("GEMINI_API_KEY not found - LLM features will be limited")
                self.llm = None
            else:
                self.llm = ChatGoogleGenerativeAI(
                    model="gemini-1.5-flash",
                    temperature=0,
                    max_tokens=4000,
                    google_api_key=api_key
                )
                logger.info("✅ LLM initialized successfully")
        except Exception as e:
            logger.warning(f"LLM initialization failed: {e} - continuing with basic Excel processing")
            self.llm = None

        # Automatically load the dataset
        self.load_dataset()
    
    def load_dataset(self) -> bool:
        """Load the USA dataset from Excel file"""
        if not os.path.exists(self.excel_file_path):
            logger.error(f"❌ CRITICAL: Excel file not found: {self.excel_file_path}")
            logger.error(f"❌ USA LLM dataset integration will NOT work without the Excel file!")
            logger.error(f"❌ Please ensure 'USA Details.xlsx' is present in the working directory")
            logger.error(f"❌ Current working directory: {os.getcwd()}")
            return False
            
        try:
            logger.info(f"Loading USA dataset from: {self.excel_file_path}")
            
            # Load all sheets
            xl_file = pd.ExcelFile(self.excel_file_path)
            
            for sheet_name in xl_file.sheet_names:
                try:
                    self.excel_data[sheet_name] = pd.read_excel(self.excel_file_path, sheet_name=sheet_name)
                    logger.info(f"Loaded sheet '{sheet_name}' with {len(self.excel_data[sheet_name])} rows")
                except Exception as e:
                    logger.error(f"Error loading sheet {sheet_name}: {e}")
            
            self.loaded = True
            return True
            
        except Exception as e:
            logger.error(f"Error loading USA dataset: {e}")
            return False
    
    def is_usa_plant(self, plant_name: str, country: str = None) -> bool:
        """Check if a plant is in the USA dataset"""
        logger.info(f"🔍 Checking if '{plant_name}' is USA plant (country: {country})")

        if not self.loaded:
            logger.warning(f"❌ USA dataset not loaded - cannot check plant: {plant_name}")
            return False

        if country and "united states" not in country.lower():
            logger.info(f"❌ Country '{country}' is not United States - skipping USA dataset")
            return False

        # Check in USA Details sheet
        if "USA Details" in self.excel_data:
            usa_details = self.excel_data["USA Details"]
            if 'Plant Name' in usa_details.columns:
                plant_exists = usa_details['Plant Name'].str.contains(
                    plant_name, case=False, na=False
                ).any()
                logger.info(f"{'✅' if plant_exists else '❌'} Plant '{plant_name}' {'found' if plant_exists else 'not found'} in USA dataset")
                return plant_exists
            else:
                logger.warning("❌ Plant Name column not found in USA Details sheet")
        else:
            logger.warning("❌ USA Details sheet not found in Excel data")

        return False
    
    def extract_organization_data(self, plant_name: str) -> Dict[str, Any]:
        """Extract organization-level data using LLM"""
        if not self.loaded or not self.llm:
            return {}
            
        try:
            # Get USA Details sheet data
            usa_details = self.excel_data.get("USA Details")
            if usa_details is None:
                return {}
            
            # Find the organization for this plant
            plant_rows = usa_details[usa_details['Plant Name'].str.contains(
                plant_name, case=False, na=False
            )] if 'Plant Name' in usa_details.columns else pd.DataFrame()
            
            if plant_rows.empty:
                return {}
            
            organization_name = plant_rows.iloc[0].get('Entity Name', 'Unknown Organization')
            
            # Get all plants under this organization
            org_plants = usa_details[usa_details['Entity Name'] == organization_name] if 'Entity Name' in usa_details.columns else pd.DataFrame()
            
            # Create prompt for LLM
            prompt = f"""
            Based on the following USA Details data, extract organization information:
            
            Organization Name: {organization_name}
            Plant Data Sample (first 10 rows):
            {org_plants.head(10).to_string()}
            
            Please provide:
            1. plants_count: Count of unique plant names under this organization
            2. plant_types: List of unique plant types for plants under this organization
            
            Return as JSON format:
            {{
                "organization_name": "{organization_name}",
                "plants_count": <number>,
                "plant_types": [<list of plant types>]
            }}
            """
            
            response = self.llm.invoke([HumanMessage(content=prompt)])
            
            # Parse LLM response
            try:
                # Extract JSON from response
                json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                    logger.info(f"LLM extracted organization data: {result}")
                    return result
            except Exception as e:
                logger.error(f"Error parsing LLM response: {e}")
            
            # Fallback: manual calculation
            unique_plants = org_plants['Plant Name'].nunique() if 'Plant Name' in org_plants.columns else 0
            plant_types = org_plants['Plant Type'].dropna().unique().tolist() if 'Plant Type' in org_plants.columns else []
            
            return {
                "organization_name": organization_name,
                "plants_count": unique_plants,
                "plant_types": plant_types
            }
            
        except Exception as e:
            logger.error(f"Error extracting organization data: {e}")
            return {}
    
    def extract_plant_data(self, plant_name: str) -> Dict[str, Any]:
        """Extract plant-level data using LLM"""
        if not self.loaded or not self.llm:
            return {}
            
        try:
            # Get USA Details sheet data
            usa_details = self.excel_data.get("USA Details")
            if usa_details is None:
                return {}
            
            # Find plant data
            plant_rows = usa_details[usa_details['Plant Name'].str.contains(
                plant_name, case=False, na=False
            )] if 'Plant Name' in usa_details.columns else pd.DataFrame()
            
            if plant_rows.empty:
                return {}
            
            # Create prompt for LLM
            prompt = f"""
            Based on the following USA Details data for plant "{plant_name}":
            
            Plant Data:
            {plant_rows.to_string()}
            
            Please extract:
            1. Latitude of the plant
            2. Longitude of the plant  
            3. Plant Type
            4. units_id: List all unit IDs and map them as sequential numbers (1, 2, 3, etc.)
            
            Return as JSON format:
            {{
                "latitude": <latitude_value>,
                "longitude": <longitude_value>,
                "plant_type": "<plant_type>",
                "units_id": [<list of unit numbers>]
            }}
            """
            
            response = self.llm.invoke([HumanMessage(content=prompt)])
            
            # Parse LLM response
            try:
                json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                    logger.info(f"LLM extracted plant data: {result}")
                    return result
            except Exception as e:
                logger.error(f"Error parsing LLM response: {e}")
            
            # Fallback: manual extraction
            first_row = plant_rows.iloc[0]
            latitude = first_row.get('Latitude')
            longitude = first_row.get('Longitude')
            plant_type = first_row.get('Plant Type')
            
            # Extract unit IDs
            unit_ids = plant_rows['Unit IDs'].dropna().unique().tolist() if 'Unit IDs' in plant_rows.columns else []
            units_id = list(range(1, len(unit_ids) + 1)) if unit_ids else [1]
            
            return {
                "latitude": float(latitude) if pd.notna(latitude) else None,
                "longitude": float(longitude) if pd.notna(longitude) else None,
                "plant_type": plant_type if pd.notna(plant_type) else None,
                "units_id": units_id
            }
            
        except Exception as e:
            logger.error(f"Error extracting plant data: {e}")
            return {}
    
    def extract_unit_data(self, plant_name: str, unit_name: str = None) -> Dict[str, Any]:
        """Extract unit-level data using LLM

        Args:
            plant_name: Name of the plant
            unit_name: Specific unit name/identifier for unit-specific data extraction
        """
        if not self.loaded or not self.llm:
            return {}

        try:
            unit_data = {}

            # 1. Extract emission_factor_coal (PLANT-LEVEL: same for all units)
            logger.info(f"🔍 Extracting emission_factor_coal (plant-level, same for all units) for {plant_name}")
            unit_data.update(self._extract_emission_factor_coal_llm(plant_name, unit_name))

            # 2. Extract emission_factor (PLANT-LEVEL: same for all units)
            logger.info(f"🧮 Calculating emission_factor (plant-level, same for all units) for {plant_name}")
            unit_data.update(self._extract_emission_factors_by_year_llm(plant_name, unit_name))

            # 3. Extract UNIT-SPECIFIC data (different for each unit)
            logger.info(f"📋 Extracting unit-specific data for {plant_name} - Unit: {unit_name}")
            unit_data.update(self._extract_capacity_llm(plant_name, unit_name))
            unit_data.update(self._extract_fuel_type_llm(plant_name, unit_name))
            unit_data.update(self._extract_commencement_date_llm(plant_name, unit_name))
            unit_data.update(self._extract_remaining_useful_life_llm(plant_name, unit_name))

            # 4. Calculate unit_lifetime (unit-specific based on unit's dates)
            unit_data.update(self._calculate_unit_lifetime_llm(unit_data))

            # 5. CRITICAL FIX: Get org_id and plant_id from database
            try:
                from agent.database_manager import get_database_manager
                db_manager = get_database_manager()

                existing_plant = db_manager.check_plant_exists(plant_name)
                if existing_plant:
                    unit_data["org_id"] = existing_plant.get("org_id", "UNKNOWN_ORG")
                    unit_data["plant_id"] = existing_plant.get("plant_id", "UNKNOWN_PLANT")
                    logger.info(f"✅ Retrieved database UIDs - org_id: {unit_data['org_id']}, plant_id: {unit_data['plant_id']}")
                else:
                    unit_data["org_id"] = "UNKNOWN_ORG"
                    unit_data["plant_id"] = "UNKNOWN_PLANT"
                    logger.warning(f"⚠️ Plant not found in database: {plant_name}")

            except Exception as e:
                logger.error(f"❌ Error retrieving database UIDs: {e}")
                unit_data["org_id"] = "UNKNOWN_ORG"
                unit_data["plant_id"] = "UNKNOWN_PLANT"

            # CRITICAL DEBUG: Log extracted values for key fields
            logger.info(f"✅ FINAL EXTRACTION RESULTS for {plant_name}/{unit_name}:")
            logger.info(f"   Total fields extracted: {len(unit_data)}")
            logger.info(f"   Field names: {list(unit_data.keys())}")

            # Log key values that user is expecting
            key_fields = ['emission_factor_coal', 'unit_lifetime', 'remaining_useful_life', 'capacity']
            for field in key_fields:
                value = unit_data.get(field, 'NOT_FOUND')
                logger.info(f"   {field}: {value}")

            # Log emission_factor years
            ef_years = [k for k in unit_data.keys() if k.startswith('emission_factor_') and k[-4:].isdigit()]
            logger.info(f"   emission_factor years: {len(ef_years)} found - {ef_years}")

            return unit_data

        except Exception as e:
            logger.error(f"Error extracting unit data: {e}")
            return {}

    def _extract_emission_factor_coal_llm(self, plant_name: str, unit_name: str = None) -> Dict[str, Any]:
        """Extract emission_factor_coal using specific 3-step process:
        1. Get Type from 2024 sheet (e.g., BIT)
        2. Find full form in Full Names sheet (BIT → Bituminous coal)
        3. Find emission factor value for that full form (Bituminous coal → 2.44)

        CRITICAL: This is PLANT-LEVEL data - same for ALL units of the plant
        """
        try:
            # CRITICAL: Check if we already have plant-level emission_factor_coal cached
            cache_key = f"emission_factor_coal_{plant_name}"
            if hasattr(self, '_plant_cache') and cache_key in self._plant_cache:
                logger.info(f"Using cached emission_factor_coal for {plant_name}: {self._plant_cache[cache_key]}")
                return {"emission_factor_coal": self._plant_cache[cache_key]}

            # Initialize cache if not exists
            if not hasattr(self, '_plant_cache'):
                self._plant_cache = {}

            # Step 1: Get fuel type from 2024 sheet ONLY
            year_2024 = self.excel_data.get("2024")
            full_names = self.excel_data.get("Full Names")

            if year_2024 is None or full_names is None:
                logger.warning("2024 sheet or Full Names sheet not available")
                return {"emission_factor_coal": None}

            # Find plant in 2024 sheet - check ALL rows for this plant
            plant_rows = year_2024[year_2024['Plant Name'].str.contains(
                plant_name, case=False, na=False
            )] if 'Plant Name' in year_2024.columns else pd.DataFrame()

            if plant_rows.empty:
                logger.warning(f"Plant '{plant_name}' not found in 2024 sheet")
                return {"emission_factor_coal": None}

            # CRITICAL FIX: Get fuel type code from coal rows (Fuel = "COL") first
            if 'Type' not in plant_rows.columns or 'Fuel' not in plant_rows.columns:
                logger.warning("Type or Fuel column not found in 2024 sheet")
                return {"emission_factor_coal": None}

            fuel_type_code = None

            # PRIORITY 1: Look for coal rows (Fuel = "COL") and get Type code from that row
            coal_rows = plant_rows[plant_rows['Fuel'] == 'COL']
            if not coal_rows.empty:
                # Get the Type code from the coal row (e.g., "SUB" for subbituminous)
                fuel_type_code = coal_rows['Type'].iloc[0]
                logger.info(f"Step 1: Found COAL fuel type code '{fuel_type_code}' for {plant_name} (from Fuel=COL row)")
            else:
                # FALLBACK: If no coal rows, this plant doesn't have coal - return None
                logger.info(f"Step 1: No coal rows (Fuel=COL) found for {plant_name} - not a coal plant")
                return {"emission_factor_coal": None}

            if not fuel_type_code:
                logger.warning(f"No Type code found in coal rows for {plant_name}")
                return {"emission_factor_coal": None}

            logger.info(f"Step 1: Found fuel type code '{fuel_type_code}' for {plant_name} in 2024 sheet")

            # Step 2: Find full form in Full Names sheet
            full_form = None
            if 'Fuel Type Full Forms' in full_names.columns:
                # Look for the fuel type code in Full Names sheet
                matching_rows = full_names[full_names['Fuel Type Full Forms'].astype(str).str.contains(
                    str(fuel_type_code), case=False, na=False
                )]

                if not matching_rows.empty:
                    # Get the full form from 'Unnamed: 7' column (as seen in test data)
                    if 'Unnamed: 7' in matching_rows.columns:
                        full_form = matching_rows.iloc[0]['Unnamed: 7']
                        if pd.notna(full_form):
                            logger.info(f"Step 2: Found full form '{full_form}' for code '{fuel_type_code}'")
                        else:
                            # Try 'Unnamed: 1' column as fallback
                            full_form = matching_rows.iloc[0]['Unnamed: 1']
                            if pd.notna(full_form):
                                logger.info(f"Step 2: Found full form '{full_form}' for code '{fuel_type_code}' (fallback)")

            if not full_form:
                logger.warning(f"Could not find full form for fuel type code '{fuel_type_code}'")
                return {"emission_factor_coal": None}

            # Step 3: Find emission factor value for the full form
            # Look in the emission factor table (first column and Unnamed: 2)
            emission_factor_value = None

            # Check first column for the full form name
            first_col = full_names.columns[0]  # '"emission_factor_coal" value based on Fuel Type '

            # CRITICAL FIX: Improved Full Names sheet lookup
            # The Full Names sheet has two types of rows:
            # 1. Rows with emission factors: 'Unnamed: 1' has coal descriptions, 'Unnamed: 2' has values
            # 2. Rows with mappings: 'Fuel Type Full Forms' has codes, 'Unnamed: 7' has descriptions

            # Strategy: Look for coal type keywords in 'Unnamed: 1' column
            coal_keywords = {
                'SUB': ['sub', 'bituminous'],
                'BIT': ['other', 'bituminous', 'coal'],
                'ANT': ['anthracite'],
                'LIG': ['lignite'],
                'RC': ['refined', 'coal'],
                'WC': ['waste', 'coal']
            }

            keywords = coal_keywords.get(fuel_type_code, [])
            if keywords:
                logger.info(f"Step 3: Looking for keywords {keywords} in Unnamed: 1 column")

                for idx, row in full_names.iterrows():
                    if pd.notna(row.get('Unnamed: 1', '')):
                        unnamed1_text = str(row['Unnamed: 1']).lower()
                        # Check if all keywords are present
                        if all(keyword in unnamed1_text for keyword in keywords):
                            if 'Unnamed: 2' in full_names.columns and pd.notna(row['Unnamed: 2']):
                                emission_factor_value = float(row['Unnamed: 2'])
                                logger.info(f"Step 3: Found emission_factor_coal value {emission_factor_value} for '{fuel_type_code}' in row {idx}: '{row['Unnamed: 1']}'")
                                break

            # Fallback: Original logic
            if emission_factor_value is None:
                for idx, row in full_names.iterrows():
                    if (pd.notna(row[first_col]) and str(full_form).lower() in str(row[first_col]).lower()) or \
                       (pd.notna(row.get('Unnamed: 1', '')) and str(full_form).lower() in str(row['Unnamed: 1']).lower()):

                        if 'Unnamed: 2' in full_names.columns and pd.notna(row['Unnamed: 2']):
                            emission_factor_value = float(row['Unnamed: 2'])
                            logger.info(f"Step 3: Found emission_factor_coal value {emission_factor_value} for '{full_form}' (fallback)")
                            break

            if emission_factor_value is not None:
                # CRITICAL: Cache the plant-level value for all units
                self._plant_cache[cache_key] = emission_factor_value
                logger.info(f"Cached emission_factor_coal for {plant_name}: {emission_factor_value}")
                return {"emission_factor_coal": emission_factor_value}
            else:
                logger.warning(f"Could not find emission factor value for '{full_form}'")
                return {"emission_factor_coal": None}

        except Exception as e:
            logger.error(f"Error extracting emission_factor_coal with LLM: {e}")
            return {"emission_factor_coal": None}

    def _extract_emission_factors_by_year_llm(self, plant_name: str, unit_name: str = None) -> Dict[str, Any]:
        """Extract emission_factor for years 2020-2024 using LLM with correct fuel type filtering

        CRITICAL: This is PLANT-LEVEL data - same for ALL units of the plant
        """
        try:
            # CRITICAL: Check if we already have plant-level emission_factors cached
            cache_key = f"emission_factors_{plant_name}"
            if hasattr(self, '_plant_cache') and cache_key in self._plant_cache:
                logger.info(f"Using cached emission_factors for {plant_name}")
                return self._plant_cache[cache_key]

            # Initialize cache if not exists
            if not hasattr(self, '_plant_cache'):
                self._plant_cache = {}

            # First get emission_factor_coal
            emission_factor_coal_data = self._extract_emission_factor_coal_llm(plant_name, unit_name)
            emission_factor_coal = emission_factor_coal_data.get('emission_factor_coal')

            if not emission_factor_coal:
                logger.warning(f"No emission_factor_coal found for {plant_name}, cannot calculate emission_factors")
                return {}

            emission_factors = {}

            # CRITICAL: Process ALL 5 years (2020-2024) with better error handling
            for year in ["2020", "2021", "2022", "2023", "2024"]:
                logger.info(f"Processing emission_factor for {plant_name} - {year}")

                year_data = self.excel_data.get(year)
                if year_data is None:
                    logger.warning(f"No data sheet found for year {year}")
                    continue

                # Find plant data for this year - be more flexible with plant name matching
                plant_rows = year_data[year_data['Plant Name'].str.contains(
                    plant_name, case=False, na=False
                )] if 'Plant Name' in year_data.columns else pd.DataFrame()

                if plant_rows.empty:
                    logger.warning(f"Plant '{plant_name}' not found in {year} sheet")
                    continue

                # Filter for coal plants only (Fuel = "COL") - as specified in user requirements
                coal_rows = plant_rows[plant_rows['Fuel'] == 'COL'] if 'Fuel' in plant_rows.columns else pd.DataFrame()

                if coal_rows.empty:
                    # If no coal data found, skip this year
                    logger.info(f"No coal data found for {plant_name} in {year}")
                    continue

                # CRITICAL: Manual calculation instead of LLM for better accuracy
                try:
                    # Find the correct column names (with or without quotes)
                    fuel_consumption_col = None
                    net_generation_col = None

                    for col in coal_rows.columns:
                        if 'electric fuel consumption quantity' in col.lower():
                            fuel_consumption_col = col
                        elif 'net generation' in col.lower() and 'megawatthours' in col.lower():
                            net_generation_col = col

                    if not fuel_consumption_col or not net_generation_col:
                        logger.warning(f"Required columns not found for {year}: fuel_consumption={fuel_consumption_col}, net_generation={net_generation_col}")
                        continue

                    # Get the values and calculate
                    fuel_consumption = coal_rows.iloc[0][fuel_consumption_col]
                    net_generation = coal_rows.iloc[0][net_generation_col]

                    if pd.notna(fuel_consumption) and pd.notna(net_generation) and float(net_generation) > 0:
                        # Formula: emission_factor = (A / B) * C
                        # A = fuel_consumption * 0.9072
                        # B = net_generation
                        # C = emission_factor_coal
                        A = float(fuel_consumption) * 0.9072
                        B = float(net_generation)
                        emission_factor = (A / B) * emission_factor_coal

                        emission_factors[f"emission_factor_{year}"] = round(emission_factor, 6)
                        logger.info(f"Calculated emission_factor_{year}: {emission_factor} for {plant_name}")
                        logger.debug(f"  A (fuel * 0.9072): {A}")
                        logger.debug(f"  B (net_generation): {B}")
                        logger.debug(f"  C (emission_factor_coal): {emission_factor_coal}")
                    else:
                        logger.warning(f"Invalid data for {year}: fuel_consumption={fuel_consumption}, net_generation={net_generation}")

                except Exception as calc_error:
                    logger.error(f"Error calculating emission_factor for {year}: {calc_error}")
                    continue

            # CRITICAL: Cache the plant-level values for all units
            if emission_factors:
                self._plant_cache[cache_key] = emission_factors
                logger.info(f"Cached emission_factors for {plant_name}: {list(emission_factors.keys())}")

            return emission_factors

        except Exception as e:
            logger.error(f"Error extracting emission_factors with LLM: {e}")
            return {}

    def _extract_capacity_llm(self, plant_name: str, unit_name: str = None) -> Dict[str, Any]:
        """Extract capacity using LLM - UNIT-SPECIFIC data from USA Details sheet"""
        try:
            usa_details = self.excel_data.get("USA Details")
            if usa_details is None:
                return {"capacity": None}

            # Find plant data
            plant_rows = usa_details[usa_details['Plant Name'].str.contains(
                plant_name, case=False, na=False
            )] if 'Plant Name' in usa_details.columns else pd.DataFrame()

            if plant_rows.empty:
                logger.warning(f"Plant '{plant_name}' not found in USA Details sheet")
                return {"capacity": None}

            # If unit_name is provided, try to find specific unit row
            target_rows = plant_rows
            if unit_name:
                # Look for unit-specific data in various possible unit columns
                unit_columns = ['Unit Code', 'Unit ID', 'Unit', 'Generator ID', 'Unit Name']
                for col in unit_columns:
                    if col in usa_details.columns:
                        unit_specific_rows = plant_rows[plant_rows[col].astype(str).str.contains(
                            str(unit_name), case=False, na=False
                        )]
                        if not unit_specific_rows.empty:
                            target_rows = unit_specific_rows
                            logger.info(f"Found unit-specific data for {unit_name} in column {col}")
                            break

            # Manual extraction first (more reliable)
            capacity_columns = ['Nameplate Capacity (MW)', 'Capacity', 'MW', 'Nameplate', 'Summer Capacity (MW)', 'Winter Capacity (MW)']
            for col in capacity_columns:
                if col in target_rows.columns:
                    capacity_val = target_rows.iloc[0][col]
                    if pd.notna(capacity_val) and isinstance(capacity_val, (int, float)):
                        logger.info(f"Found capacity {capacity_val} for {plant_name}/{unit_name} in column {col}")
                        return {"capacity": float(capacity_val)}

            # LLM fallback if manual extraction fails
            prompt = f"""
            Extract capacity information for plant "{plant_name}", unit: {unit_name}

            Unit-specific data from USA Details sheet:
            {target_rows.to_string()}

            Find the capacity value for this specific unit. Look for columns like Nameplate Capacity, Summer Capacity, Winter Capacity, etc.

            Return ONLY a JSON object:
            {{"capacity": <numeric_value_or_null>}}
            """

            response = self.llm.invoke([HumanMessage(content=prompt)])

            # Parse LLM response with better regex
            try:
                json_match = re.search(r'\{[^}]*\}', response.content)
                if json_match:
                    result = json.loads(json_match.group())
                    logger.info(f"LLM extracted capacity: {result}")
                    return result
            except Exception as e:
                logger.error(f"Error parsing LLM response for capacity: {e}")

            return {"capacity": None}

        except Exception as e:
            logger.error(f"Error extracting capacity with LLM: {e}")
            return {"capacity": None}

    def _extract_fuel_type_llm(self, plant_name: str, unit_name: str = None) -> Dict[str, Any]:
        """Extract fuel_type using LLM"""
        try:
            fuel_data = {"fuel": None, "type": None}

            # Check year sheets for fuel and type information
            for year in ["2024", "2023", "2022", "2021", "2020"]:
                year_data = self.excel_data.get(year)
                if year_data is None:
                    continue

                plant_rows = year_data[year_data['Plant Name'].str.contains(
                    plant_name, case=False, na=False
                )] if 'Plant Name' in year_data.columns else pd.DataFrame()

                if plant_rows.empty:
                    continue

                prompt = f"""
                Extract fuel type information for plant "{plant_name}" from {year} data:

                Plant Data:
                {plant_rows.head().to_string()}

                Find:
                1. fuel: Value from "Fuel" column
                2. type: Value from "Type" column

                Return as JSON:
                {{
                    "fuel": "<fuel_value>",
                    "type": "<type_value>"
                }}
                """

                response = self.llm.invoke([HumanMessage(content=prompt)])

                # Parse LLM response with better regex
                try:
                    json_match = re.search(r'\{[^}]*\}', response.content)
                    if json_match:
                        result = json.loads(json_match.group())
                        if result.get("fuel") and result.get("type"):
                            fuel_data = result
                            logger.info(f"LLM extracted fuel_type: {result}")
                            break
                except Exception as e:
                    logger.error(f"Error parsing LLM response for fuel_type: {e}")
                    logger.debug(f"LLM response content: {response.content[:200]}...")

            return {"fuel_type": fuel_data}

        except Exception as e:
            logger.error(f"Error extracting fuel_type with LLM: {e}")
            return {"fuel_type": {"fuel": None, "type": None}}

    def _extract_commencement_date_llm(self, plant_name: str, unit_name: str = None) -> Dict[str, Any]:
        """Extract commencement_date using LLM"""
        try:
            usa_details = self.excel_data.get("USA Details")
            if usa_details is None:
                return {"commencement_date": None}

            plant_rows = usa_details[usa_details['Plant Name'].str.contains(
                plant_name, case=False, na=False
            )] if 'Plant Name' in usa_details.columns else pd.DataFrame()

            if plant_rows.empty:
                return {"commencement_date": None}

            prompt = f"""
            Extract commencement date for plant "{plant_name}":

            Plant Data:
            {plant_rows.to_string()}

            Find:
            1. Operating Month
            2. Operating Year

            Convert to timestamp format: yyyy-mm-ddThh:mm:ss.msZ
            Use day 01 and time 00:00:00.000Z

            Return as JSON:
            {{
                "commencement_date": "yyyy-mm-ddThh:mm:ss.000Z",
                "operating_month": <month>,
                "operating_year": <year>
            }}
            """

            response = self.llm.invoke([HumanMessage(content=prompt)])

            # Parse LLM response with better regex
            try:
                json_match = re.search(r'\{[^}]*\}', response.content)
                if json_match:
                    result = json.loads(json_match.group())
                    logger.info(f"LLM extracted commencement_date: {result}")
                    return {"commencement_date": result.get("commencement_date")}
            except Exception as e:
                logger.error(f"Error parsing LLM response for commencement_date: {e}")
                logger.debug(f"LLM response content: {response.content[:200]}...")

            # Fallback: manual extraction
            first_row = plant_rows.iloc[0]
            operating_month = first_row.get('Operating Month', 1)
            operating_year = first_row.get('Operating Year', 2000)

            if pd.notna(operating_year) and pd.notna(operating_month):
                commencement_date = f"{int(operating_year):04d}-{int(operating_month):02d}-01T00:00:00.000Z"
                return {"commencement_date": commencement_date}

            return {"commencement_date": None}

        except Exception as e:
            logger.error(f"Error extracting commencement_date with LLM: {e}")
            return {"commencement_date": None}

    def _extract_remaining_useful_life_llm(self, plant_name: str, unit_name: str = None) -> Dict[str, Any]:
        """Extract remaining_useful_life with proper timestamp format and unit-specific data"""
        try:
            usa_details = self.excel_data.get("USA Details")
            if usa_details is None:
                return {"remaining_useful_life": None}

            # CRITICAL: Find unit-specific data if unit_name is provided
            if unit_name:
                # Look for unit-specific rows
                unit_rows = usa_details[
                    (usa_details['Plant Name'].str.contains(plant_name, case=False, na=False)) &
                    (usa_details['Unit Name'].str.contains(unit_name, case=False, na=False))
                ] if 'Plant Name' in usa_details.columns and 'Unit Name' in usa_details.columns else pd.DataFrame()

                if not unit_rows.empty:
                    plant_rows = unit_rows
                    logger.info(f"Found unit-specific data for {plant_name} - {unit_name}")
                else:
                    # Fallback to plant-level data
                    plant_rows = usa_details[usa_details['Plant Name'].str.contains(
                        plant_name, case=False, na=False
                    )] if 'Plant Name' in usa_details.columns else pd.DataFrame()
                    logger.info(f"Using plant-level data for {plant_name} - {unit_name}")
            else:
                plant_rows = usa_details[usa_details['Plant Name'].str.contains(
                    plant_name, case=False, na=False
                )] if 'Plant Name' in usa_details.columns else pd.DataFrame()

            if plant_rows.empty:
                logger.warning(f"No data found for plant '{plant_name}' in USA Details sheet")
                return {"remaining_useful_life": None}

            # CRITICAL: Manual extraction with better error handling (skip LLM for reliability)
            first_row = plant_rows.iloc[0]

            # Try to find retirement columns with various possible names
            retirement_month = None
            retirement_year = None
            operating_month = None
            operating_year = None

            for col in first_row.index:
                col_lower = str(col).lower()
                if 'planned retirement month' in col_lower or 'retirement month' in col_lower:
                    retirement_month = first_row[col]
                elif 'planned retirement year' in col_lower or 'retirement year' in col_lower:
                    retirement_year = first_row[col]
                elif 'operating month' in col_lower:
                    operating_month = first_row[col]
                elif 'operating year' in col_lower:
                    operating_year = first_row[col]

            logger.info(f"Retirement data for {plant_name}: month={retirement_month}, year={retirement_year}")
            logger.info(f"Operating data for {plant_name}: month={operating_month}, year={operating_year}")

            # CRITICAL: If retirement data is present, use it
            if pd.notna(retirement_year) and pd.notna(retirement_month):
                try:
                    year = int(float(retirement_year))
                    month = int(float(retirement_month))
                    # Ensure month is valid (1-12)
                    if month < 1 or month > 12:
                        month = 1
                    remaining_useful_life = f"{year:04d}-{month:02d}-01T00:00:00.000Z"
                    logger.info(f"Using retirement date: {remaining_useful_life}")
                    return {"remaining_useful_life": remaining_useful_life}
                except (ValueError, TypeError) as e:
                    logger.error(f"Error parsing retirement date: {e}")

            # CRITICAL: If not present, add 50 years to operating date
            if pd.notna(operating_year):
                try:
                    base_year = int(float(operating_year))
                    base_month = int(float(operating_month)) if pd.notna(operating_month) else 1
                    # Ensure month is valid (1-12)
                    if base_month < 1 or base_month > 12:
                        base_month = 1

                    retirement_year = base_year + 50
                    remaining_useful_life = f"{retirement_year:04d}-{base_month:02d}-01T00:00:00.000Z"
                    logger.info(f"Using operating date + 50 years: {remaining_useful_life}")
                    return {"remaining_useful_life": remaining_useful_life}
                except (ValueError, TypeError) as e:
                    logger.error(f"Error calculating operating date + 50: {e}")

            logger.warning(f"Could not determine remaining_useful_life for {plant_name}")
            return {"remaining_useful_life": None}

        except Exception as e:
            logger.error(f"Error extracting remaining_useful_life: {e}")
            return {"remaining_useful_life": None}

    def _calculate_unit_lifetime_llm(self, unit_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate unit_lifetime using LLM - CRITICAL FIX: Handle null values and perform calculation"""
        try:
            commencement_date = unit_data.get('commencement_date')
            remaining_useful_life = unit_data.get('remaining_useful_life')

            # CRITICAL FIX: If unit_lifetime is not found, calculate it as (remaining_useful_life - commencement_date)
            if not commencement_date or not remaining_useful_life:
                logger.warning(f"Missing dates for unit_lifetime calculation: commencement={commencement_date}, remaining={remaining_useful_life}")
                return {"unit_lifetime": None}

            # Manual calculation first (more reliable than LLM)
            try:
                from datetime import datetime
                start_date = datetime.fromisoformat(commencement_date.replace('Z', '+00:00'))
                end_date = datetime.fromisoformat(remaining_useful_life.replace('Z', '+00:00'))

                # Calculate difference in years (remaining_useful_life - commencement_date)
                lifetime_years = (end_date - start_date).days / 365.25
                lifetime_value = round(lifetime_years, 1)

                logger.info(f"✅ Calculated unit_lifetime: {lifetime_value} years ({commencement_date} to {remaining_useful_life})")
                return {"unit_lifetime": lifetime_value}

            except Exception as e:
                logger.error(f"Error in manual unit_lifetime calculation: {e}")

            # LLM fallback if manual calculation fails
            prompt = f"""
            Calculate unit_lifetime as the difference between two dates:

            remaining_useful_life: {remaining_useful_life}
            commencement_date: {commencement_date}

            Instructions:
            1. Parse both dates (format: yyyy-mm-ddThh:mm:ss.000Z)
            2. Calculate: remaining_useful_life - commencement_date
            3. Return the result as a decimal number in years (e.g., 25.5)

            Return ONLY a JSON object:
            {{"unit_lifetime": <years_as_decimal>}}
            """

            response = self.llm.invoke([HumanMessage(content=prompt)])

            # Parse LLM response with better regex
            try:
                json_match = re.search(r'\{[^}]*\}', response.content)
                if json_match:
                    result = json.loads(json_match.group())
                    logger.info(f"LLM calculated unit_lifetime: {result}")
                    return result
            except Exception as e:
                logger.error(f"Error parsing LLM response for unit_lifetime: {e}")

            return {"unit_lifetime": None}

        except Exception as e:
            logger.error(f"Error calculating unit_lifetime with LLM: {e}")
            return {"unit_lifetime": None}


# Global instance
usa_llm_dataset = USALLMDatasetIntegration()

def initialize_usa_llm_dataset() -> bool:
    """Initialize the USA LLM dataset integration"""
    return usa_llm_dataset.load_dataset()

def is_usa_plant_llm(plant_name: str, country: str = None) -> bool:
    """Check if a plant is in the USA dataset using LLM integration"""
    if not usa_llm_dataset.loaded:
        usa_llm_dataset.load_dataset()
    return usa_llm_dataset.is_usa_plant(plant_name, country)

def extract_usa_organization_data_llm(plant_name: str) -> Dict[str, Any]:
    """Extract organization data using LLM"""
    if not usa_llm_dataset.loaded:
        usa_llm_dataset.load_dataset()
    return usa_llm_dataset.extract_organization_data(plant_name)

def extract_usa_plant_data_llm(plant_name: str) -> Dict[str, Any]:
    """Extract plant data using LLM"""
    if not usa_llm_dataset.loaded:
        usa_llm_dataset.load_dataset()
    return usa_llm_dataset.extract_plant_data(plant_name)

def extract_usa_unit_data_llm(plant_name: str, unit_name: str = None) -> Dict[str, Any]:
    """Extract unit data using LLM"""
    if not usa_llm_dataset.loaded:
        usa_llm_dataset.load_dataset()
    return usa_llm_dataset.extract_unit_data(plant_name, unit_name)
