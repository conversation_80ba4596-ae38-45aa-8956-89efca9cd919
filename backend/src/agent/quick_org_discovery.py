"""
Quick Organization Discovery Module

This module provides lightweight organization discovery functionality
to quickly extract basic organization information and plant lists
without running the full 3-level extraction pipeline.
"""

import os
import pandas as pd
from typing import Dict, List, Optional
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.runnables import RunnableConfig
from agent.tools_and_schemas import SearchQueryList
from agent.configuration import Configuration
from agent.prompts import get_current_date
from agent.utils import get_research_topic
from agent.state import OverallState
from dotenv import load_dotenv

load_dotenv()

class QuickOrgDiscovery:
    """
    Quick organization discovery for generating UIDs without full extraction
    
    This class provides fast organization discovery to get:
    1. Organization name
    2. Country
    3. Basic plant list for the organization
    
    Optimized for speed (30-60 seconds) vs full extraction (5-10 minutes)
    """
    
    def __init__(self):
        """Initialize quick discovery with optimized settings"""
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",  # Fast model
            temperature=0,
            max_retries=2,  # Reduced retries for speed
            api_key=os.getenv("GEMINI_API_KEY"),
        )
        self.structured_llm = self.llm.with_structured_output(SearchQueryList)
    
    def generate_quick_queries(self, plant_name: str) -> List[str]:
        """
        Generate 2-3 targeted queries for quick organization discovery
        
        Args:
            plant_name: Name of the power plant
            
        Returns:
            List of optimized search queries
        """
        current_date = get_current_date()
        
        prompt = f"""Generate 3-4 PRECISION targeted search queries using EXPERT PROMPT ENGINEERING to comprehensively find:

1. OWNERSHIP & ORGANIZATION: The parent company/organization that owns "{plant_name}"
2. GEOGRAPHIC CONTEXT: Country, region, and operational context
3. COMPLETE PORTFOLIO: ALL power plant sites owned by the same organization

EXPERT PROMPT ENGINEERING APPROACH:
Use MULTI-ANGLE QUESTIONING in each query to capture maximum information:

QUERY DESIGN PRINCIPLES:
- DIRECT QUESTIONING: Use proven formats like "X is owned by?"
- PORTFOLIO DISCOVERY: "List all power plants owned by..."
- CONTEXTUAL SEARCH: Include technical, regulatory, and geographic angles
- COMPREHENSIVE SCOPE: Cover subsidiaries, joint ventures, and holding companies

CRITICAL DISTINCTION:
- POWER PLANT SITE = Physical facility with unique name (e.g., "Itabo Power Station")
- GENERATING UNIT = Individual generators within a site (e.g., "Unit 1", "Unit 2")
- Count DISTINCT PLANT SITES, not individual generating units

SEARCH STRATEGY - MULTI-ANGLE COVERAGE:
1. OWNERSHIP DISCOVERY: Direct ownership with company identification
2. PORTFOLIO MAPPING: Complete plant portfolio with technical context
3. REGULATORY/TECHNICAL: Official databases with operational details

Format your response as a JSON object with:
- "rationale": Brief explanation of the multi-angle search strategy
- "query": List of 3-4 precision queries covering all angles comprehensively

Plant Name: {plant_name}
Current Date: {current_date}
"""
        
        try:
            result = self.structured_llm.invoke(prompt)

            # CRITICAL FIX: Check if result is None or missing query attribute
            if result is None:
                print(f"❌ LLM returned None result for quick queries")
                raise Exception("LLM returned None result")

            if not hasattr(result, 'query'):
                print(f"❌ LLM result missing 'query' attribute: {type(result)}")
                raise Exception("LLM result missing 'query' attribute")

            if not isinstance(result.query, list):
                print(f"❌ LLM result.query is not a list: {type(result.query)}")
                raise Exception("LLM result.query is not a list")

            return result.query[:7]  # Increased to 7 queries max for comprehensive discovery

        except Exception as e:
            print(f"❌ Error generating quick queries: {e}")
            # ENHANCED fallback queries with PROVEN ChatGPT prompts
            return [
                # PRIMARY OWNERSHIP QUERY (Your proven prompt #1)
                f'"{plant_name}" is owned and operated by?',

                # DIRECT OWNERSHIP VARIATIONS
                f'"{plant_name}" owner company organization parent',
                f'"{plant_name}" owned by operated by company name',

                # LOCATION AND CONTEXT
                f'"{plant_name}" location country address coordinates',

                # PORTFOLIO DISCOVERY (Will be enhanced with org_name later)
                f'"{plant_name}" parent company power plant portfolio facilities',
                f'"{plant_name}" operator utility company other plants sites',

                # REGULATORY AND OFFICIAL SOURCES
                f'"{plant_name}" regulatory filing power plant list directory'
            ]
    
    def extract_org_info_from_results(self, plant_name: str, search_results: List[Dict]) -> Dict:
        """
        Extract organization information from search results
        
        Args:
            plant_name: Original plant name
            search_results: Web search results
            
        Returns:
            Dictionary with organization information
        """
        # Combine all search results into context
        context = ""
        for result in search_results:
            context += f"Title: {result.get('title', '')}\n"
            context += f"Content: {result.get('content', '')}\n"
            context += f"URL: {result.get('url', '')}\n\n"
        
        if not context.strip():
            return {
                "org_name": "Unknown Organization",
                "country": "Unknown",
                "plants": [{"name": plant_name, "status": "operational"}]
            }
        
        extraction_prompt = f"""EXPERT EXTRACTION TASK: Extract COMPREHENSIVE information about "{plant_name}" using ADVANCED PROMPT ENGINEERING techniques.

MULTI-DIMENSIONAL EXTRACTION APPROACH:
Apply SYSTEMATIC INFORMATION EXTRACTION across multiple dimensions to ensure NO DATA LOSS:

🏢 DIMENSION 1: ORGANIZATIONAL INTELLIGENCE
- LEGAL ENTITY: Extract the COMPLETE legal name of the owning organization
- CORPORATE STRUCTURE: Include subsidiaries, holding companies, joint ventures
- OWNERSHIP CHAIN: Parent company → Operating company → Plant ownership
- LEGAL SUFFIXES: Preserve "Limited", "Ltd", "Corporation", "S.p.A.", "GmbH", etc.

🌍 DIMENSION 2: GEOGRAPHIC & OPERATIONAL CONTEXT
- COUNTRY: Primary country of operation
- REGION/STATE: Sub-national location details
- OPERATIONAL STATUS: Current operational state of the plant

🏭 DIMENSION 3: PORTFOLIO INTELLIGENCE (CRITICAL FOR ENTITY EXTRACTION)
- PLANT SITES: ALL distinct physical power plant facilities owned by the same organization
- SITE DISTINCTION: Physical locations with unique names (NOT individual generating units)
- TECHNOLOGY MIX: Different types of plants (coal, gas, nuclear, renewable)
- GEOGRAPHIC SPREAD: Plants across different regions/countries

EXPERT EXTRACTION RULES:
✅ PRECISION: Use EXACT legal company names as they appear in official documents
✅ COMPLETENESS: Extract ALL plant sites mentioned, even with partial information
✅ DISTINCTION: Count PLANT SITES (e.g., "Brindisi Power Station") NOT units (e.g., "Unit 1", "Unit 2")
✅ STATUS INTELLIGENCE: Default to "operational" unless explicitly stated otherwise
✅ CONTEXT PRESERVATION: Maintain rich contextual information for downstream processing

MULTI-ANGLE SEARCH ANALYSIS:
Analyze the search results from MULTIPLE PERSPECTIVES:
1. DIRECT MENTIONS: Explicit ownership statements
2. CONTEXTUAL CLUES: Implied relationships and portfolio references
3. REGULATORY DATA: Official filings and government databases
4. INDUSTRY SOURCES: Trade publications and industry reports

STRUCTURED OUTPUT FORMAT:
Return a JSON object with COMPREHENSIVE data structure:
{{
    "org_name": "Complete Legal Organization Name",
    "country": "Country Name",
    "plants": [
        {{"name": "Plant Site Name 1", "status": "operational"}},
        {{"name": "Plant Site Name 2", "status": "operational"}},
        {{"name": "Plant Site Name 3", "status": "under_construction"}}
    ]
}}

SEARCH RESULTS FOR ANALYSIS:
{context[:8000]}

TARGET PLANT: {plant_name}

EXTRACTION INSTRUCTION: Apply your expertise to extract MAXIMUM information while maintaining PRECISION and COMPLETENESS.
"""
        
        try:
            # Use regular LLM for extraction (not structured output for flexibility)
            response = self.llm.invoke(extraction_prompt)
            
            # Try to parse JSON from response
            import json
            import re
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
            if json_match:
                org_info = json.loads(json_match.group())
                
                # Validate required fields
                if not org_info.get("org_name") or org_info["org_name"] == "Unknown Organization":
                    org_info["org_name"] = f"Unknown Organization ({plant_name})"

                if not org_info.get("country") or org_info["country"] == "Unknown":
                    org_info["country"] = "Unknown"

                if not org_info.get("plants"):
                    org_info["plants"] = [{"name": plant_name, "status": "operational"}]

                # CRITICAL FIX: Ensure all plants have proper status (default to operational)
                for plant in org_info.get("plants", []):
                    if not plant.get("status") or plant.get("status") in ["unknown", "Unknown", ""]:
                        plant["status"] = "operational"
                        print(f"🔧 Fixed plant status: '{plant.get('name')}' → operational")

                return org_info
            else:
                raise ValueError("No JSON found in response")
                
        except Exception as e:
            print(f"❌ Error extracting org info: {e}")
            # Fallback response
            return {
                "org_name": f"Unknown Organization ({plant_name})",
                "country": "Unknown",
                "plants": [{"name": plant_name, "status": "operational"}]
            }
    
    def discover_organization(self, plant_name: str, web_search_function) -> Dict:
        """
        Main method to discover organization information quickly
        
        Args:
            plant_name: Name of the power plant
            web_search_function: Function to perform web searches
            
        Returns:
            Dictionary with organization information
        """
        print(f"🔍 Starting quick organization discovery for: {plant_name}")
        
        # Step 1: Generate targeted queries
        queries = self.generate_quick_queries(plant_name)
        print(f"📝 Generated {len(queries)} targeted queries")
        
        # Step 2: Execute searches
        all_results = []
        for i, query in enumerate(queries, 1):
            print(f"🌐 Executing query {i}/{len(queries)}: {query[:50]}...")
            try:
                results = web_search_function(query)
                if results:
                    all_results.extend(results[:3])  # Limit results per query
            except Exception as e:
                print(f"⚠️ Search failed for query {i}: {e}")
                continue
        
        print(f"📊 Collected {len(all_results)} search results")
        
        # Step 3: Extract organization information
        org_info = self.extract_org_info_from_results(plant_name, all_results)

        # Step 4: ENHANCED SEARCH - Use your proven prompt #2 for comprehensive plant discovery
        if org_info.get('org_name') and org_info['org_name'] not in ['Unknown Organization', f'Unknown Organization ({plant_name})']:
            org_name = org_info['org_name']
            print(f"🔍 Phase 2: Comprehensive plant search for {org_name}")

            # Your proven prompt #2: List all plants owned by the organization
            comprehensive_queries = [
                f'List out all the power plants which are currently owned and operational by {org_name}',
                f'{org_name} power plants portfolio complete list all facilities',
                f'{org_name} electricity generation plants operational facilities worldwide',
                f'{org_name} coal gas nuclear renewable power plants owned operated',
                f'{org_name} power generation assets plant sites locations'
            ]

            # Execute comprehensive search
            comprehensive_results = []
            for i, query in enumerate(comprehensive_queries, 1):
                print(f"🌐 Comprehensive query {i}/{len(comprehensive_queries)}: {query[:60]}...")
                try:
                    results = web_search_function(query)
                    if results:
                        comprehensive_results.extend(results[:2])  # Limit results per query
                except Exception as e:
                    print(f"⚠️ Comprehensive search failed for query {i}: {e}")
                    continue

            # Re-extract with enhanced results
            if comprehensive_results:
                print(f"📊 Collected {len(comprehensive_results)} additional comprehensive results")
                enhanced_org_info = self.extract_org_info_from_results(plant_name, all_results + comprehensive_results)

                # Use enhanced results if they found more plants
                if len(enhanced_org_info.get('plants', [])) > len(org_info.get('plants', [])):
                    org_info = enhanced_org_info
                    print(f"✅ Enhanced search found more plants: {len(org_info['plants'])}")

        print(f"✅ Quick discovery complete:")
        print(f"   Organization: {org_info['org_name']}")
        print(f"   Country: {org_info['country']}")
        print(f"   Plants found: {len(org_info['plants'])}")

        return org_info

# Global instance
quick_discovery = QuickOrgDiscovery()

def perform_quick_org_discovery(plant_name: str, web_search_function) -> Dict:
    """
    Enhanced convenience function for quick organization discovery with USA dataset priority

    Args:
        plant_name: Name of the power plant
        web_search_function: Function to perform web searches

    Returns:
        Dictionary with organization information
    """
    try:
        # STEP 1: Check if this is a USA plant first
        print(f"🔍 Checking if {plant_name} is in USA dataset...")

        try:
            from agent.usa_dataset_pipeline import usa_pipeline

            if usa_pipeline.is_usa_plant(plant_name):
                print(f"🇺🇸 Found {plant_name} in USA dataset - using USA pipeline")

                # Extract organization data from USA dataset
                org_data = usa_pipeline.extract_organization_data(plant_name)

                if org_data:
                    # Convert to quick discovery format
                    result = {
                        "org_name": org_data["organization_name"],
                        "country": org_data["country_name"],
                        "plants": []  # Will be populated with all plants from the organization
                    }

                    # Get all plants for this organization from USA dataset
                    try:
                        if usa_pipeline.usa_details is not None:
                            org_plants = usa_pipeline.usa_details[
                                usa_pipeline.usa_details['Entity Name'] == org_data["organization_name"]
                            ]

                            unique_plants = org_plants['Plant Name'].unique()
                            for plant in unique_plants:
                                if pd.notna(plant):
                                    result["plants"].append({
                                        "name": plant,
                                        "status": "operational"
                                    })
                    except Exception as e:
                        print(f"⚠️ Could not extract plant list from USA dataset: {e}")
                        # Add at least the input plant
                        result["plants"] = [{"name": plant_name, "status": "operational"}]

                    print(f"✅ USA dataset discovery complete:")
                    print(f"   Organization: {result['org_name']}")
                    print(f"   Country: {result['country']}")
                    print(f"   Plants found: {len(result['plants'])}")

                    return result

        except ImportError:
            print("⚠️ USA pipeline not available, falling back to web search")
        except Exception as e:
            print(f"⚠️ Error checking USA dataset: {e}, falling back to web search")

        # STEP 2: Fallback to existing web search logic
        print(f"🌐 Using web search pipeline for {plant_name}")
        return quick_discovery.discover_organization(plant_name, web_search_function)

    except Exception as e:
        print(f"❌ Error in enhanced quick discovery: {e}")
        # Final fallback
        return {
            "org_name": f"Unknown Organization ({plant_name})",
            "country": "Unknown",
            "plants": [{"name": plant_name, "status": "operational"}]
        }